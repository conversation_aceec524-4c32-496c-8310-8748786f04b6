# 🔄 Meeting.End 事件处理流程完整梳理

## 📋 概述

本文档详细梳理了ZoomBus系统中meeting.end事件的完整处理流程，包括多个触发路径、核心处理逻辑、关键组件分析和数据流向。

## 🎯 触发路径分析

### 1. Zoom Webhook 自动触发路径 (主要路径)

```mermaid
graph TD
    A[Zoom平台发送meeting.ended webhook] --> B[WebhookController.handleMeetingEnded]
    B --> C[参数验证: meetingUuid, meetingId, hostId]
    C --> D[查找会议记录: findByZoomMeetingUuid]
    D --> E{会议记录存在?}
    E -->|是| F[检查会议状态: STARTED?]
    E -->|否| G[备用处理: ZoomMeetingEventService]
    F -->|是| H[ZoomMeetingService.handleMeetingEnded]
    F -->|否| I[跳过处理，记录日志]
    H --> J[分布式锁保护]
    J --> K[handleMeetingEndedInternal]
```

### 2. 前端手动结束路径

```mermaid
graph TD
    A[用户点击结束会议按钮] --> B[ZoomMeetingController.endMeeting]
    B --> C[状态验证: STARTED/WAITING]
    C --> D[ZoomMeetingService.endMeeting]
    D --> E[分布式锁保护]
    E --> F[调用Zoom API结束会议]
    F --> G[handleMeetingEndedInternal]
```

### 3. UUID方式结束路径

```mermaid
graph TD
    A[API调用: /zoom-meetings/uuid/{uuid}/end] --> B[ZoomMeetingUuidController.endMeetingByUuid]
    B --> C[ZoomMeetingService.endMeetingByUuid]
    C --> D[分布式锁保护]
    D --> E[检查是否需要调用Zoom API]
    E --> F[调用Zoom API (如需要)]
    F --> G[endMeetingWithSeparateTransaction]
    G --> H[AsyncMeetingProcessService.asyncProcessMeetingEndByUuid]
```

## 🏗️ 核心处理逻辑

### handleMeetingEndedInternal() 主流程

这是所有结束路径的核心汇聚点：

```java
private void handleMeetingEndedInternal(String meetingUuid, EndingSource source, boolean needZoomApiCall) {
    // 1. 查找会议记录
    Optional<ZoomMeeting> meetingOpt = zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid);
    
    // 2. 状态验证 (STARTED状态)
    if (meeting.getStatus() != ZoomMeeting.MeetingStatus.STARTED) {
        return; // 跳过处理
    }
    
    // 3. 调用Zoom API (如需要)
    if (needZoomApiCall) {
        zoomApiService.endMeeting(meeting.getZoomMeetingId());
    }
    
    // 4. 更新会议状态和时长
    meeting.setStatus(ZoomMeeting.MeetingStatus.ENDED);
    meeting.setEndTime(LocalDateTime.now());
    calculateDuration(meeting);
    
    // 5. 保存会议记录
    zoomMeetingRepository.save(meeting);
    
    // 6. 后续处理 (同步或异步)
    // - 停止计费监控
    // - 执行结算
    // - 释放ZoomUser资源
}
```

## 🔧 关键组件详解

### 1. 分布式锁机制

**目的**: 防止并发处理同一个会议
**实现**: Redis分布式锁
**锁键**: `meeting:end:{meetingUuid}`
**超时**: 30秒

```java
String lockKey = DistributedLockService.getMeetingEndLockKey(meetingUuid);
String lockValue = distributedLockService.tryLockWithRetry(lockKey, 30, 3, 100);
```

### 2. 状态管理

**会议状态流转**:
```
PENDING → STARTED → ENDED → SETTLED
```

**状态检查逻辑**:
- 只处理 `STARTED` 状态的会议
- 跳过已经 `ENDED` 的会议
- 记录状态不匹配的情况

### 3. 时长计算

```java
if (meeting.getStartTime() != null) {
    Duration duration = Duration.between(meeting.getStartTime(), endTime);
    long minutes = Math.max(0, duration.toMinutes());
    meeting.setDurationMinutes((int) minutes);
} else {
    meeting.setDurationMinutes(0); // 未开始即结束
}
```

## 🔄 后续处理流程

### 同步处理 (handleMeetingEndedInternal)

1. **停止计费监控**
   ```java
   billingMonitorService.stopBillingMonitor(meetingId);
   ```

2. **执行结算**
   ```java
   meetingSettlementService.settleMeeting(meetingId);
   ```

3. **释放ZoomUser资源** (仅PMI会议)
   ```java
   if (isPmiMeeting(meeting)) {
       zoomUserPmiService.releaseZoomUser(meetingId);
   }
   ```

### 异步处理 (AsyncMeetingProcessService)

**优势**: 提高API响应速度
**风险**: 可能导致事务一致性问题

```java
@Async
public void asyncProcessMeetingEndByUuid(String meetingUuid) {
    // 1. 停止计费监控
    billingMonitorService.stopBillingMonitor(meetingId);
    
    // 2. 执行结算
    meetingSettlementService.settleMeeting(meetingId);
    
    // 3. 释放ZoomUser账号 (仅PMI会议)
    if (isPmiMeeting(meeting)) {
        zoomUserPmiService.releaseZoomUser(meetingId);
    }
    
    // 4. 触发会议报告获取 (延迟5分钟)
    triggerMeetingReportFetch(meetingUuid, meeting.getZoomMeetingId());
}
```

## 💰 结算处理详解

### MeetingSettlementService

**功能**: 根据计费模式执行不同结算策略

```java
public SettlementResult settleMeeting(Long meetingId) {
    // 根据计费模式进行结算
    if (meeting.getBillingMode() == PmiRecord.BillingMode.LONG) {
        result = settleLongBillingMeeting(meeting, pmiRecord);
    } else {
        result = settleTimeBillingMeeting(meeting, pmiRecord);
    }
    
    // 更新会议状态
    if (result.isSuccess()) {
        meeting.setIsSettled(true);
        meeting.setStatus(ZoomMeeting.MeetingStatus.SETTLED);
    }
}
```

**结算类型**:
- **LONG模式**: 长租计费，按月/年收费
- **TIME模式**: 按时计费，按分钟收费

## 🔓 资源释放详解

### ZoomUserPmiService.releaseZoomUser()

**适用范围**: 仅PMI类型会议
**核心逻辑**:

```java
public void releaseZoomUser(Long meetingId) {
    List<ZoomUser> assignedUsers = zoomUserRepository.findByCurrentMeetingId(meetingId);
    
    for (ZoomUser user : assignedUsers) {
        // 1. 恢复原始PMI
        if (user.getOriginalPmi() != null) {
            restoreOriginalPmi(user);
            user.setCurrentPmi(user.getOriginalPmi());
        }
        
        // 2. 更新使用统计
        user.updateUsageStats(0);
        
        // 3. 设置状态为可用
        user.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
        user.setCurrentMeetingId(null);
        user.setLastUsedTime(LocalDateTime.now());
    }
}
```

## 📊 监控和日志

### 关键监控指标

1. **Webhook事件统计**
   ```java
   webhookMonitorService.recordWebhookEvent("meeting.ended", success);
   ```

2. **事务监控**
   ```java
   transactionMonitorService.recordTransactionSuccess("endMeetingByUuid-" + meetingUuid);
   ```

3. **性能监控**
   - 处理耗时统计
   - 分布式锁获取时间
   - API调用响应时间

### 日志记录

**操作ID追踪**: 每个处理流程生成唯一操作ID
**详细时间记录**: 记录各个步骤的执行时间
**错误处理**: 完整的异常堆栈和上下文信息

## ⚠️ 注意事项

### 1. 并发安全
- 使用分布式锁防止重复处理
- 状态检查避免重复操作

### 2. 事务一致性
- 同步处理保证强一致性
- 异步处理可能存在最终一致性问题

### 3. 错误处理
- Zoom API调用失败不阻断本地处理
- 资源释放失败记录日志但不影响主流程
- 异步处理失败不影响会议状态更新

### 4. 性能考虑
- 异步处理提高响应速度
- 分布式锁避免资源竞争
- 批量操作优化数据库访问

这个流程确保了会议结束事件的可靠处理，同时兼顾了性能和数据一致性。
