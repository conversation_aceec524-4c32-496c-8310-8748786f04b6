#!/bin/bash

echo "=== PMI任务调度系统简化改进部署脚本 ==="
echo

# 1. 备份当前版本
echo "1. 备份当前生产环境JAR包..."
ssh <EMAIL> "cd /root/zoombus && cp zoombus-1.0.0.jar zoombus-1.0.0.jar.backup.$(date +%Y%m%d_%H%M%S)"

# 2. 上传新版本
echo "2. 上传改进版本..."
scp target/zoombus-1.0.0.jar <EMAIL>:/root/zoombus/

# 3. 重启服务
echo "3. 重启服务..."
ssh <EMAIL> "cd /root/zoombus && kill \$(cat zoombus.pid 2>/dev/null) 2>/dev/null || echo 'no_running_service'; sleep 5; ./stable_zoombus_start.sh"

# 4. 等待启动
echo "4. 等待服务启动..."
sleep 30

# 5. 验证服务状态
echo "5. 验证服务状态..."
ssh <EMAIL> "ps aux | grep zoombus-1.0.0.jar | grep -v grep && netstat -tlnp 2>/dev/null | grep :8080"

# 6. 检查应用日志
echo "6. 检查应用启动日志..."
ssh <EMAIL> "tail -n 20 /root/zoombus/logs/zoombus-application.log | grep -E 'Started ZoomBusApplication|ERROR|Exception' || echo 'no_critical_issues'"

# 7. 验证改进效果
echo "7. 验证改进效果..."
echo "请观察以下指标："
echo "- 任务执行日志中是否出现'延迟3秒后执行'"
echo "- 是否还有'任务不存在'错误"
echo "- 开启任务是否显示为'发现待执行的开启任务'"

echo
echo "=== 部署完成 ==="
echo "请继续监控系统运行状况，特别关注："
echo "1. 新创建的PMI任务是否正常执行"
echo "2. 是否还有竞态条件问题"
echo "3. 任务状态与实际结果是否一致"
