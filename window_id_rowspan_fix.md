# 🔧 窗口ID列跨行显示修复

## 🐛 问题描述

在PMI任务监控页面中，窗口ID列出现了重复显示的问题：
- 窗口ID被展示了2遍
- 导致第二行及后续行向右偏移了一列
- 表格列对齐错乱

## 🔍 问题原因

原始实现中的`rowSpan`逻辑有问题：
1. 当`rowSpan`为0时，单元格仍然被渲染
2. Ant Design的`rowSpan`需要正确的值才能实现跨行效果
3. 数据分组逻辑没有按照任务的原始顺序正确处理

## ✅ 修复方案

### 1. 优化数据处理逻辑

**新的`processTasksForRowSpan`函数**:
```javascript
const processTasksForRowSpan = (taskList) => {
  if (!taskList || taskList.length === 0) return [];
  
  // 创建处理后的任务列表
  const processedTasks = [];
  let currentWindowId = null;
  let windowTaskCount = 0;
  let windowStartIndex = 0;
  
  // 遍历任务列表，按顺序处理
  taskList.forEach((task, index) => {
    if (task.pmiWindowId !== currentWindowId) {
      // 处理前一个窗口的任务
      if (currentWindowId !== null && windowTaskCount > 0) {
        // 为前一个窗口的第一个任务设置rowSpan
        if (processedTasks[windowStartIndex]) {
          processedTasks[windowStartIndex].windowRowSpan = windowTaskCount;
        }
        // 为前一个窗口的其他任务设置rowSpan为0
        for (let i = windowStartIndex + 1; i < processedTasks.length; i++) {
          processedTasks[i].windowRowSpan = 0;
        }
      }
      
      // 开始新窗口
      currentWindowId = task.pmiWindowId;
      windowTaskCount = 1;
      windowStartIndex = processedTasks.length;
    } else {
      windowTaskCount++;
    }
    
    processedTasks.push({ ...task });
  });
  
  // 处理最后一个窗口
  if (currentWindowId !== null && windowTaskCount > 0) {
    processedTasks[windowStartIndex].windowRowSpan = windowTaskCount;
    for (let i = windowStartIndex + 1; i < processedTasks.length; i++) {
      if (processedTasks[i].pmiWindowId === currentWindowId) {
        processedTasks[i].windowRowSpan = 0;
      }
    }
  }
  
  return processedTasks;
};
```

### 2. 简化列渲染逻辑

**窗口ID列定义**:
```javascript
{
  title: '窗口ID',
  dataIndex: 'pmiWindowId',
  key: 'pmiWindowId',
  width: 80,
  render: (windowId, record) => {
    return {
      children: <span style={{ fontFamily: 'monospace' }}>{windowId}</span>,
      props: {
        rowSpan: record.windowRowSpan,
      },
    };
  }
}
```

## 🔄 修复效果

**修复前**:
```
┌─────────┬─────────┬────────┬──────────┐
│ 窗口ID  │ 窗口ID  │ 任务ID │ 任务类型  │  ← 窗口ID重复
├─────────┼─────────┼────────┼──────────┤
│   123   │   123   │   456  │ 窗口开启  │
│         │   123   │   457  │ 窗口关闭  │  ← 列错位
└─────────┴─────────┴────────┴──────────┘
```

**修复后**:
```
┌─────────┬────────┬──────────┬─────────┐
│ 窗口ID  │ 任务ID │ 任务类型  │ 状态    │
├─────────┼────────┼──────────┼─────────┤
│   123   │   456  │ 窗口开启  │ 已完成  │
│         │   457  │ 窗口关闭  │ 已调度  │  ← 正确跨行
├─────────┼────────┼──────────┼─────────┤
│   124   │   458  │ 窗口开启  │ 已调度  │
│         │   459  │ 窗口关闭  │ 已调度  │
└─────────┴────────┴──────────┴─────────┘
```

## 🎯 关键改进

1. **顺序处理**: 按照任务的原始顺序处理，不破坏数据排序
2. **正确分组**: 连续的相同窗口ID任务被正确分组
3. **精确rowSpan**: 第一个任务设置正确的rowSpan值，其他任务设置为0
4. **简化渲染**: 移除不必要的条件判断，直接使用rowSpan值

## 📋 修改文件

- `frontend/src/pages/PmiTaskMonitor.jsx` - 修复数据处理和列渲染逻辑

这个修复确保了窗口ID列的正确跨行显示，解决了列错位问题，提供了清晰的任务归属关系展示。
