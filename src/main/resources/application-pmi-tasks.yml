# WebSocket配置
websocket:
  enabled: true

# PMI任务调度配置
pmi:
  task:
    scheduling:
      # 是否启用精准定时任务机制（启用精准调度）
      enable-precise-scheduling: true

      # 是否启用任务监控
      enable-task-monitoring: true
      
      # 任务清理配置
      cleanup:
        enabled: true
        retention-days: 30
        cron-expression: "0 0 2 * * ?"  # 每天凌晨2点执行
      
      # 任务重试配置
      retry:
        max-retry-count: 3
        retry-interval-minutes: 5
        exponential-backoff: true
      
      # 性能配置
      performance:
        batch-size: 100
        thread-pool-size: 15              # 增加主线程池大小
        enable-task-cache: true
        cache-expiration-minutes: 10

      # 立即执行配置
      immediate-execution:
        enabled: true                     # 是否启用立即执行机制
        tolerance-minutes: 2              # 时间容差（分钟）

        # 线程池配置（优化后）
        thread-pool:
          core-size: 8                    # 增加核心线程数
          max-size: 15                    # 增加最大线程数
          queue-capacity: 150             # 增加队列容量
          thread-name-prefix: "immediate-task-"
          keep-alive-seconds: 60
          allow-core-thread-time-out: false

      # 监控配置（优化后）
      monitoring:
        enabled: true
        stuck-task-check-interval-ms: 120000    # 2分钟检查一次（原3分钟）
        health-check-interval-ms: 600000        # 10分钟健康检查
        metrics-interval-seconds: 60
        record-execution-details: true

        # 告警配置
        alert:
          enabled: true
          failure-rate-threshold: 5.0           # 失败率阈值（百分比）
          expired-task-threshold: 10            # 过期任务数量阈值（每小时）
          avg-execution-time-threshold: 5       # 平均执行时间阈值（秒）
          queue-full-threshold: 80.0            # 队列满阈值（百分比）

# 日志配置
logging:
  level:
    com.zoombus.service.DynamicTaskManager: INFO
    com.zoombus.service.PmiWindowTaskExecutor: INFO
    com.zoombus.service.PmiWindowTaskSchedulingService: INFO
    com.zoombus.scheduler.PmiTaskCleanupScheduler: INFO
