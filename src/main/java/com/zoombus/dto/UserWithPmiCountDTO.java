package com.zoombus.dto;

import com.zoombus.entity.User;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户信息包含PMI数量的DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserWithPmiCountDTO {
    
    private Long id;
    private String username;
    private String email;
    private String fullName;
    private String department;
    private String phone;
    private User.UserStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Long pmiCount; // PMI数量
    
    /**
     * 从User实体创建DTO
     */
    public static UserWithPmiCountDTO fromUser(User user, Long pmiCount) {
        UserWithPmiCountDTO dto = new UserWithPmiCountDTO();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setEmail(user.getEmail());
        dto.setFullName(user.getFullName());
        dto.setDepartment(user.getDepartment());
        dto.setPhone(user.getPhone());
        dto.setStatus(user.getStatus());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        dto.setPmiCount(pmiCount);
        return dto;
    }
}
