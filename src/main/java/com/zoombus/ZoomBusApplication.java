package com.zoombus;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableScheduling
@EnableAsync
@EnableRetry

public class ZoomBusApplication {
    public static void main(String[] args) {
        SpringApplication.run(ZoomBusApplication.class, args);
    }
}
