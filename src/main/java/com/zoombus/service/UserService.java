package com.zoombus.service;

import com.zoombus.dto.CreateUserRequest;
import com.zoombus.dto.UserWithPmiCountDTO;
import com.zoombus.entity.User;
import com.zoombus.repository.UserRepository;
import com.zoombus.repository.PmiRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final PmiRecordRepository pmiRecordRepository;
    
    /**
     * 创建用户
     */
    public User createUser(CreateUserRequest request) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在: " + request.getUsername());
        }
        
        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("邮箱已存在: " + request.getEmail());
        }
        
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setFullName(request.getFullName());
        user.setDepartment(request.getDepartment());
        user.setPhone(request.getPhone());
        user.setStatus(User.UserStatus.ACTIVE);
        
        User savedUser = userRepository.save(user);
        log.info("用户创建成功: {}", savedUser.getUsername());
        return savedUser;
    }
    
    /**
     * 根据ID获取用户
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserById(Long id) {
        return userRepository.findById(id);
    }
    
    /**
     * 根据用户名获取用户
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }
    
    /**
     * 根据邮箱获取用户
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    
    /**
     * 获取所有用户
     */
    @Transactional(readOnly = true)
    public Page<User> getAllUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    /**
     * 获取所有用户（包含PMI数量）
     */
    @Transactional(readOnly = true)
    public Page<UserWithPmiCountDTO> getAllUsersWithPmiCount(Pageable pageable) {
        Page<User> userPage = userRepository.findAll(pageable);

        List<UserWithPmiCountDTO> userDTOs = userPage.getContent().stream()
                .map(user -> {
                    Long pmiCount = pmiRecordRepository.countByUserId(user.getId());
                    return UserWithPmiCountDTO.fromUser(user, pmiCount);
                })
                .collect(Collectors.toList());

        return new PageImpl<>(userDTOs, pageable, userPage.getTotalElements());
    }
    
    /**
     * 根据状态获取用户
     */
    @Transactional(readOnly = true)
    public List<User> getUsersByStatus(User.UserStatus status) {
        return userRepository.findByStatus(status);
    }
    
    /**
     * 根据部门获取用户
     */
    @Transactional(readOnly = true)
    public List<User> getUsersByDepartment(String department) {
        return userRepository.findByDepartment(department);
    }
    
    /**
     * 搜索用户
     */
    @Transactional(readOnly = true)
    public List<User> searchUsers(String name) {
        return userRepository.findByNameContaining(name);
    }
    
    /**
     * 更新用户
     */
    public User updateUser(Long id, CreateUserRequest request) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + id));
        
        // 检查用户名是否被其他用户使用
        if (!user.getUsername().equals(request.getUsername()) && 
            userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在: " + request.getUsername());
        }
        
        // 检查邮箱是否被其他用户使用
        if (!user.getEmail().equals(request.getEmail()) && 
            userRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("邮箱已存在: " + request.getEmail());
        }
        
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setFullName(request.getFullName());
        user.setDepartment(request.getDepartment());
        user.setPhone(request.getPhone());
        
        User updatedUser = userRepository.save(user);
        log.info("用户更新成功: {}", updatedUser.getUsername());
        return updatedUser;
    }
    
    /**
     * 更新用户状态
     */
    public User updateUserStatus(Long id, User.UserStatus status) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + id));
        
        user.setStatus(status);
        User updatedUser = userRepository.save(user);
        log.info("用户状态更新成功: {} -> {}", updatedUser.getUsername(), status);
        return updatedUser;
    }
    
    /**
     * 删除用户
     */
    public void deleteUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + id));
        
        userRepository.delete(user);
        log.info("用户删除成功: {}", user.getUsername());
    }
}
