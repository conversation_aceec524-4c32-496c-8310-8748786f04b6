package com.zoombus.service;


import com.zoombus.dto.MonitoringData;
import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.repository.PmiScheduleWindowRepository;
import com.zoombus.repository.PmiScheduleWindowTaskRepository;
import com.zoombus.service.impl.DynamicTaskManagerImpl;
import com.zoombus.service.impl.PmiWindowTaskExecutorImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * PMI任务调度监控服务
 * 负责监控和处理长时间处于SCHEDULED状态的任务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiTaskSchedulingMonitorService {

    private final PmiScheduleWindowTaskRepository taskRepository;
    private final PmiScheduleWindowRepository windowRepository;
    private final DynamicTaskManagerImpl dynamicTaskManager;
    private final PmiWindowTaskExecutorImpl taskExecutor;
    private final WebSocketService webSocketService;

    // 简单的执行状态跟踪
    private final Set<Long> executingTasks = ConcurrentHashMap.newKeySet();
    private final ScheduledExecutorService delayedExecutor = Executors.newScheduledThreadPool(5);

    /**
     * 定期检查长时间处于SCHEDULED状态的任务
     * 间隔可配置，默认2分钟
     */
    @Scheduled(fixedRateString = "#{@pmiTaskSchedulingConfig.getMonitoring().getStuckTaskCheckIntervalMs()}")
    @Transactional
    public void checkStuckScheduledTasks() {
        try {
            log.debug("开始检查长时间处于SCHEDULED状态的PMI任务");
            
            // 查找长时间处于SCHEDULED状态的任务（固定5分钟阈值）
            LocalDateTime stuckThreshold = LocalDateTime.now().minusMinutes(5);
            List<PmiScheduleWindowTask> stuckTasks = taskRepository.findStuckScheduledTasks(stuckThreshold);
            
            if (stuckTasks.isEmpty()) {
                log.debug("没有发现长时间处于SCHEDULED状态的任务");
                return;
            }
            
            log.warn("发现 {} 个长时间处于SCHEDULED状态的任务", stuckTasks.size());
            
            for (PmiScheduleWindowTask task : stuckTasks) {
                handleStuckScheduledTask(task);
            }
            
        } catch (Exception e) {
            log.error("检查长时间SCHEDULED状态任务失败", e);
        }
    }

    /**
     * 处理长时间处于SCHEDULED状态的任务
     */
    private void handleStuckScheduledTask(PmiScheduleWindowTask task) {
        try {
            LocalDateTime now = LocalDateTime.now();
            long delayMinutes = java.time.Duration.between(task.getScheduledTime(), now).toMinutes();
            
            if (task.getTaskType() == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
                log.info("发现待执行的开启任务: taskId={}, type={}, scheduledTime={}, currentTime={}",
                        task.getId(), task.getTaskType(), task.getScheduledTime(), LocalDateTime.now());
            } else {
                log.warn("处理长时间SCHEDULED状态任务: taskId={}, type={}, scheduledTime={}, delay={}分钟",
                        task.getId(), task.getTaskType(), task.getScheduledTime(), delayMinutes);
            }
            
            // 检查任务是否已过期
            if (task.getScheduledTime().isBefore(now)) {
                // 任务已过期，尝试立即执行或标记为失败
                handleExpiredScheduledTask(task, delayMinutes);
            } else {
                // 任务未过期但长时间处于SCHEDULED状态，可能是调度失败
                handleSchedulingFailedTask(task);
            }
            
        } catch (Exception e) {
            log.error("处理长时间SCHEDULED状态任务失败: taskId={}", task.getId(), e);
        }
    }

    /**
     * 处理已过期的SCHEDULED任务
     */
    private void handleExpiredScheduledTask(PmiScheduleWindowTask task, long delayMinutes) {
        try {
            // 检查任务是否正在执行中（避免重复执行）
            if (isTaskCurrentlyExecuting(task.getId())) {
                log.info("任务正在执行中，跳过: taskId={}", task.getId());
                return;
            }

            if (task.getTaskType() == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
                // 开启任务：只要当前时间未超过窗口结束时间就正常执行
                // PMI计划可能是当日0点开始的整日计划，开启task执行时间早于安排时间是正常的
                if (isOpenTaskStillValid(task)) {
                    log.info("开启任务仍在有效期内，延迟3秒后执行: taskId={}, scheduledTime={}, currentTime={}",
                            task.getId(), task.getScheduledTime(), LocalDateTime.now());
                    // 延迟3秒执行，确保数据库事务已提交
                    scheduleDelayedExecution(task, 3000, () ->
                        taskExecutor.executeOpenTask(task.getPmiWindowId(), task.getId()));
                } else {
                    log.warn("开启任务已超过窗口关闭时间，标记为失败: taskId={}, scheduledTime={}, currentTime={}",
                            task.getId(), task.getScheduledTime(), LocalDateTime.now());
                    task.markAsFailed("任务已超过窗口关闭时间");
                    taskRepository.save(task);
                }
            } else {
                // 关闭任务：只需要考虑PMI当前windows id仍然是本task对应的windows
                if (isCloseTaskStillValid(task)) {
                    log.info("关闭任务仍然有效，延迟3秒后执行: taskId={}, scheduledTime={}, currentTime={}",
                            task.getId(), task.getScheduledTime(), LocalDateTime.now());
                    // 延迟3秒执行，确保数据库事务已提交
                    scheduleDelayedExecution(task, 3000, () ->
                        taskExecutor.executeCloseTask(task.getPmiWindowId(), task.getId()));
                } else {
                    log.warn("关闭任务已无效，标记为失败: taskId={}, scheduledTime={}, currentTime={}",
                            task.getId(), task.getScheduledTime(), LocalDateTime.now());
                    task.markAsFailed("任务已无效，窗口已变更");
                    taskRepository.save(task);
                }
            }

        } catch (Exception e) {
            log.error("处理过期SCHEDULED任务失败: taskId={}", task.getId(), e);
            task.markAsFailed("处理过期任务时发生异常: " + e.getMessage());
            taskRepository.save(task);
        }
    }

    /**
     * 处理调度失败的任务
     */
    private void handleSchedulingFailedTask(PmiScheduleWindowTask task) {
        try {
            log.info("尝试重新调度任务: taskId={}, scheduledTime={}", task.getId(), task.getScheduledTime());
            
            // 尝试重新调度任务
            boolean rescheduled = dynamicTaskManager.rescheduleStuckTask(task);
            
            if (rescheduled) {
                log.info("任务重新调度成功: taskId={}", task.getId());
            } else {
                log.warn("任务重新调度失败，标记为失败: taskId={}", task.getId());
                task.markAsFailed("重新调度失败");
                taskRepository.save(task);
            }
            
        } catch (Exception e) {
            log.error("重新调度任务失败: taskId={}", task.getId(), e);
            task.markAsFailed("重新调度时发生异常: " + e.getMessage());
            taskRepository.save(task);
        }
    }

    /**
     * 检查开启任务是否仍然有效
     */
    private boolean isOpenTaskStillValid(PmiScheduleWindowTask task) {
        try {
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(task.getPmiWindowId());
            if (!windowOpt.isPresent()) {
                return false;
            }
            
            PmiScheduleWindow window = windowOpt.get();
            LocalDateTime now = LocalDateTime.now();
            
            // 检查是否在窗口结束时间之前
            return window.getEndDateTime().isAfter(now);
            
        } catch (Exception e) {
            log.error("检查开启任务有效性失败: taskId={}", task.getId(), e);
            return false;
        }
    }

    /**
     * 检查关闭任务是否仍然有效
     * 只需要考虑PMI当前windows id仍然是本task对应的windows
     */
    private boolean isCloseTaskStillValid(PmiScheduleWindowTask task) {
        try {
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(task.getPmiWindowId());
            if (!windowOpt.isPresent()) {
                return false;
            }
            
            PmiScheduleWindow window = windowOpt.get();
            
            // 检查窗口是否仍然是ACTIVE状态
            return window.getStatus() == PmiScheduleWindow.WindowStatus.ACTIVE;
            
        } catch (Exception e) {
            log.error("检查关闭任务必要性失败: taskId={}", task.getId(), e);
            return false;
        }
    }

    /**
     * 定期检查任务调度健康状态
     * 间隔可配置，默认10分钟
     */
    @Scheduled(fixedRateString = "#{@pmiTaskSchedulingConfig.getMonitoring().getHealthCheckIntervalMs()}")
    public void checkTaskSchedulingHealth() {
        try {
            log.debug("开始检查PMI任务调度健康状态");
            
            // 统计各状态任务数量
            long scheduledCount = taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.SCHEDULED);
            long executingCount = taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.EXECUTING);
            long failedCount = taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.FAILED);
            
            log.info("PMI任务状态统计: SCHEDULED={}, EXECUTING={}, FAILED={}", 
                    scheduledCount, executingCount, failedCount);
            
            // 检查是否有异常情况
            if (scheduledCount > 100) {
                log.warn("SCHEDULED状态任务过多: {}, 可能存在调度问题", scheduledCount);
                sendSchedulingAlert("SCHEDULED状态任务过多", scheduledCount);
            }
            
            if (executingCount > 50) {
                log.warn("EXECUTING状态任务过多: {}, 可能存在执行阻塞", executingCount);
                sendSchedulingAlert("EXECUTING状态任务过多", executingCount);
            }
            
            if (failedCount > 20) {
                log.warn("FAILED状态任务过多: {}, 需要关注任务失败原因", failedCount);
                sendSchedulingAlert("FAILED状态任务过多", failedCount);
            }
            
        } catch (Exception e) {
            log.error("检查任务调度健康状态失败", e);
        }
    }

    /**
     * 发送调度告警
     */
    private void sendSchedulingAlert(String message, long count) {
        try {
            if (webSocketService != null) {
                // 创建告警信息
                MonitoringData.AlertInfo alert = MonitoringData.AlertInfo.builder()
                        .alertId("PMI_TASK_SCHEDULING_" + System.currentTimeMillis())
                        .level("WARNING")
                        .title("PMI任务调度告警")
                        .message(message + ": " + count)
                        .source("PmiTaskSchedulingMonitorService")
                        .alertTime(LocalDateTime.now())
                        .acknowledged(false)
                        .build();

                webSocketService.pushAlert(alert);
            }
        } catch (Exception e) {
            log.error("发送调度告警失败", e);
        }
    }

    /**
     * 获取任务调度统计信息
     */
    public TaskSchedulingStats getTaskSchedulingStats() {
        try {
            TaskSchedulingStats stats = new TaskSchedulingStats();
            stats.setScheduledCount(taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.SCHEDULED));
            stats.setExecutingCount(taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.EXECUTING));
            stats.setCompletedCount(taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.COMPLETED));
            stats.setFailedCount(taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.FAILED));
            stats.setCancelledCount(taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.CANCELLED));
            
            // 计算最近1小时的任务统计
            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
            stats.setRecentFailedCount(taskRepository.countRecentFailedTasks(oneHourAgo));
            stats.setRecentCompletedCount(taskRepository.countRecentCompletedTasks(oneHourAgo));
            
            return stats;
            
        } catch (Exception e) {
            log.error("获取任务调度统计信息失败", e);
            return new TaskSchedulingStats();
        }
    }

    /**
     * 任务调度统计信息
     */
    public static class TaskSchedulingStats {
        private long scheduledCount;
        private long executingCount;
        private long completedCount;
        private long failedCount;
        private long cancelledCount;
        private long recentFailedCount;
        private long recentCompletedCount;

        // Getters and Setters
        public long getScheduledCount() { return scheduledCount; }
        public void setScheduledCount(long scheduledCount) { this.scheduledCount = scheduledCount; }
        
        public long getExecutingCount() { return executingCount; }
        public void setExecutingCount(long executingCount) { this.executingCount = executingCount; }
        
        public long getCompletedCount() { return completedCount; }
        public void setCompletedCount(long completedCount) { this.completedCount = completedCount; }
        
        public long getFailedCount() { return failedCount; }
        public void setFailedCount(long failedCount) { this.failedCount = failedCount; }
        
        public long getCancelledCount() { return cancelledCount; }
        public void setCancelledCount(long cancelledCount) { this.cancelledCount = cancelledCount; }
        
        public long getRecentFailedCount() { return recentFailedCount; }
        public void setRecentFailedCount(long recentFailedCount) { this.recentFailedCount = recentFailedCount; }

        public long getRecentCompletedCount() { return recentCompletedCount; }
        public void setRecentCompletedCount(long recentCompletedCount) { this.recentCompletedCount = recentCompletedCount; }
    }

    /**
     * 检查任务是否正在执行中
     */
    private boolean isTaskCurrentlyExecuting(Long taskId) {
        return executingTasks.contains(taskId);
    }

    /**
     * 延迟执行任务
     */
    private void scheduleDelayedExecution(PmiScheduleWindowTask task, long delayMs, Runnable execution) {
        Long taskId = task.getId();

        // 标记任务为执行中
        executingTasks.add(taskId);

        delayedExecutor.schedule(() -> {
            try {
                log.info("开始延迟执行任务: taskId={}", taskId);
                execution.run();
            } catch (Exception e) {
                log.error("延迟执行任务失败: taskId={}", taskId, e);
            } finally {
                // 移除执行标记
                executingTasks.remove(taskId);
            }
        }, delayMs, TimeUnit.MILLISECONDS);

        log.info("任务已安排延迟执行: taskId={}, delayMs={}", taskId, delayMs);
    }
}
