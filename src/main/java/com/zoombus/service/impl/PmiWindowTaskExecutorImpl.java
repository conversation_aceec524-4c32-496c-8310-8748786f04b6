package com.zoombus.service.impl;

import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.entity.PmiRecord;
import com.zoombus.repository.PmiScheduleWindowRepository;
import com.zoombus.repository.PmiScheduleWindowTaskRepository;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.service.PmiWindowTaskExecutor;
import com.zoombus.service.PmiBillingModeService;
import com.zoombus.service.DistributedLockService;
import com.zoombus.service.PmiTaskWebSocketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * PMI窗口任务执行器实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiWindowTaskExecutorImpl implements PmiWindowTaskExecutor {
    
    private final PmiScheduleWindowRepository windowRepository;
    private final PmiScheduleWindowTaskRepository taskRepository;
    private final PmiRecordRepository pmiRecordRepository;
    private final PmiBillingModeService pmiBillingModeService;
    private final DistributedLockService distributedLockService;

    @Autowired(required = false)
    private PmiTaskWebSocketService webSocketService;
    
    @Override
    @Transactional
    public void executeOpenTask(Long windowId, Long taskId) {
        log.info("开始执行PMI窗口开启任务: windowId={}, taskId={}", windowId, taskId);

        // 使用分布式锁防止重复执行
        String lockKey = "task:" + taskId;
        String lockValue = distributedLockService.tryLock(lockKey, 1800); // 30分钟锁定时间

        if (lockValue == null) {
            log.warn("获取任务锁失败，可能正在被其他实例执行: taskId={}", taskId);
            return;
        }

        try {
            // 更新任务状态为执行中
            Optional<PmiScheduleWindowTask> taskOpt = taskRepository.findById(taskId);
            if (!taskOpt.isPresent()) {
                log.error("任务不存在: taskId={}", taskId);
                return;
            }
            
            PmiScheduleWindowTask task = taskOpt.get();

            // 幂等性检查：如果任务已经成功执行，直接返回
            if (task.getStatus() == PmiScheduleWindowTask.TaskStatus.COMPLETED) {
                log.info("任务已成功执行，跳过: taskId={}", taskId);
                return;
            }

            // 如果任务不是SCHEDULED状态，也跳过执行
            if (task.getStatus() != PmiScheduleWindowTask.TaskStatus.SCHEDULED) {
                log.info("任务状态不允许执行: taskId={}, status={}", taskId, task.getStatus());
                return;
            }

            task.markAsExecuting();
            taskRepository.save(task);

            // 推送任务状态变化
            if (webSocketService != null) {
                webSocketService.pushTaskStatusChange(task);
            }
            
            // 获取PMI窗口信息
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(windowId);
            if (!windowOpt.isPresent()) {
                log.error("PMI窗口不存在: windowId={}", windowId);
                task.markAsFailed("PMI窗口不存在");
                taskRepository.save(task);
                return;
            }
            
            PmiScheduleWindow window = windowOpt.get();
            
            // 检查窗口状态
            if (window.getStatus() != PmiScheduleWindow.WindowStatus.PENDING) {
                log.warn("PMI窗口状态不正确，跳过开启: windowId={}, status={}", windowId, window.getStatus());
                task.markAsCompleted();
                taskRepository.save(task);
                return;
            }
            
            // 获取PMI记录
            Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findById(window.getPmiRecordId());
            if (!pmiRecordOpt.isPresent()) {
                log.error("PMI记录不存在: pmiRecordId={}", window.getPmiRecordId());
                task.markAsFailed("PMI记录不存在");
                taskRepository.save(task);
                return;
            }
            
            PmiRecord pmiRecord = pmiRecordOpt.get();
            
            // 执行PMI开启逻辑
            executePmiOpen(window, pmiRecord);
            
            // 更新任务状态为完成
            task.markAsCompleted();
            taskRepository.save(task);

            // 推送任务完成状态
            if (webSocketService != null) {
                webSocketService.pushTaskStatusChange(task);
            }

            log.info("PMI窗口开启任务执行成功: windowId={}, taskId={}", windowId, taskId);

        } catch (Exception e) {
            log.error("执行PMI窗口开启任务失败: windowId={}, taskId={}", windowId, taskId, e);

            // 更新任务状态为失败
            Optional<PmiScheduleWindowTask> taskOpt = taskRepository.findById(taskId);
            if (taskOpt.isPresent()) {
                PmiScheduleWindowTask task = taskOpt.get();
                task.markAsFailed("执行失败: " + e.getMessage());
                taskRepository.save(task);

                // 推送任务失败状态和告警
                if (webSocketService != null) {
                    webSocketService.pushTaskStatusChange(task);
                    webSocketService.pushTaskFailureAlert(task);
                }
            }
        } finally {
            // 释放分布式锁
            distributedLockService.releaseLock(lockKey, lockValue);
        }
    }
    
    @Override
    @Transactional
    public void executeCloseTask(Long windowId, Long taskId) {
        log.info("开始执行PMI窗口关闭任务: windowId={}, taskId={}", windowId, taskId);

        // 使用分布式锁防止重复执行
        String lockKey = "task:" + taskId;
        String lockValue = distributedLockService.tryLock(lockKey, 1800); // 30分钟锁定时间

        if (lockValue == null) {
            log.warn("获取任务锁失败，可能正在被其他实例执行: taskId={}", taskId);
            return;
        }

        try {
            // 更新任务状态为执行中
            Optional<PmiScheduleWindowTask> taskOpt = taskRepository.findById(taskId);
            if (!taskOpt.isPresent()) {
                log.error("任务不存在: taskId={}", taskId);
                return;
            }
            
            PmiScheduleWindowTask task = taskOpt.get();

            // 幂等性检查：如果任务已经成功执行，直接返回
            if (task.getStatus() == PmiScheduleWindowTask.TaskStatus.COMPLETED) {
                log.info("任务已成功执行，跳过: taskId={}", taskId);
                return;
            }

            // 如果任务不是SCHEDULED状态，也跳过执行
            if (task.getStatus() != PmiScheduleWindowTask.TaskStatus.SCHEDULED) {
                log.info("任务状态不允许执行: taskId={}, status={}", taskId, task.getStatus());
                return;
            }

            task.markAsExecuting();
            taskRepository.save(task);

            // 推送任务状态变化
            if (webSocketService != null) {
                webSocketService.pushTaskStatusChange(task);
            }
            
            // 获取PMI窗口信息
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(windowId);
            if (!windowOpt.isPresent()) {
                log.error("PMI窗口不存在: windowId={}", windowId);
                task.markAsFailed("PMI窗口不存在");
                taskRepository.save(task);
                return;
            }
            
            PmiScheduleWindow window = windowOpt.get();
            
            // 检查窗口状态
            if (window.getStatus() != PmiScheduleWindow.WindowStatus.ACTIVE) {
                log.warn("PMI窗口状态不正确，跳过关闭: windowId={}, status={}", windowId, window.getStatus());
                task.markAsCompleted();
                taskRepository.save(task);
                return;
            }
            
            // 获取PMI记录
            Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findById(window.getPmiRecordId());
            if (!pmiRecordOpt.isPresent()) {
                log.error("PMI记录不存在: pmiRecordId={}", window.getPmiRecordId());
                task.markAsFailed("PMI记录不存在");
                taskRepository.save(task);
                return;
            }
            
            PmiRecord pmiRecord = pmiRecordOpt.get();
            
            // 执行PMI关闭逻辑
            executePmiClose(window, pmiRecord);
            
            // 更新任务状态为完成
            task.markAsCompleted();
            taskRepository.save(task);
            
            log.info("PMI窗口关闭任务执行成功: windowId={}, taskId={}", windowId, taskId);

        } catch (Exception e) {
            log.error("执行PMI窗口关闭任务失败: windowId={}, taskId={}", windowId, taskId, e);

            // 更新任务状态为失败
            Optional<PmiScheduleWindowTask> taskOpt = taskRepository.findById(taskId);
            if (taskOpt.isPresent()) {
                PmiScheduleWindowTask task = taskOpt.get();
                task.markAsFailed("执行失败: " + e.getMessage());
                taskRepository.save(task);
            }
        } finally {
            // 释放分布式锁
            distributedLockService.releaseLock(lockKey, lockValue);
        }
    }
    
    /**
     * 执行PMI开启逻辑
     */
    private void executePmiOpen(PmiScheduleWindow window, PmiRecord pmiRecord) {
        log.info("执行PMI开启逻辑: windowId={}, pmiNumber={}", window.getId(), pmiRecord.getPmiNumber());
        
        // 更新PMI记录状态为ACTIVE
        pmiRecord.setStatus(PmiRecord.PmiStatus.ACTIVE);
        pmiRecord.setLastUsedAt(LocalDateTime.now());
        
        // 使用精确的窗口到期时间
        LocalDateTime windowExpireTime = window.getEndDateTime();
        
        // 切换到LONG计费模式
        pmiBillingModeService.switchToLongBilling(pmiRecord.getId(), window.getId(), windowExpireTime);
        
        // 更新窗口状态为ACTIVE
        window.setStatus(PmiScheduleWindow.WindowStatus.ACTIVE);
        window.setActualStartTime(LocalDateTime.now());
        window.setErrorMessage(null);
        
        // 保存更新
        pmiRecordRepository.save(pmiRecord);
        windowRepository.save(window);
        
        log.info("PMI开启成功: windowId={}, pmiNumber={}, pmiStatus={}", 
                window.getId(), pmiRecord.getPmiNumber(), pmiRecord.getStatus());
    }
    
    /**
     * 执行PMI关闭逻辑
     */
    private void executePmiClose(PmiScheduleWindow window, PmiRecord pmiRecord) {
        log.info("执行PMI关闭逻辑: windowId={}, pmiNumber={}", window.getId(), pmiRecord.getPmiNumber());
        
        // 更新窗口状态为COMPLETED
        window.setStatus(PmiScheduleWindow.WindowStatus.COMPLETED);
        window.setActualEndTime(LocalDateTime.now());
        
        // 保存窗口更新
        windowRepository.save(window);
        
        // 检查该PMI是否还有其他活跃窗口
        List<PmiScheduleWindow> activeWindows = windowRepository.findByPmiRecordIdAndStatus(
                pmiRecord.getId(), PmiScheduleWindow.WindowStatus.ACTIVE);
        
        if (activeWindows.isEmpty()) {
            // 没有活跃窗口，需要切换计费模式
            log.info("PMI {} 没有活跃窗口，切换计费模式", pmiRecord.getId());
            
            // 恢复到原始计费模式
            pmiBillingModeService.switchToOriginalBilling(pmiRecord.getId());
            
            // 根据原始计费模式和可用时长决定PMI状态
            PmiRecord updatedRecord = pmiRecordRepository.findById(pmiRecord.getId()).orElse(null);
            if (updatedRecord != null) {
                if (updatedRecord.getBillingMode() == PmiRecord.BillingMode.FREE) {
                    // FREE模式保持ACTIVE状态
                    log.info("PMI {} 关闭窗口后恢复到FREE模式，保持ACTIVE状态", pmiRecord.getId());
                } else if (updatedRecord.getBillingMode() == PmiRecord.BillingMode.BY_TIME) {
                    // BY_TIME模式根据可用时长决定状态
                    if (updatedRecord.getAvailableMinutes() != null && updatedRecord.getAvailableMinutes() > 0) {
                        log.info("PMI {} 关闭窗口后恢复到BY_TIME模式，可用时长: {} 分钟",
                                pmiRecord.getId(), updatedRecord.getAvailableMinutes());
                    } else {
                        // 没有可用时长，设置为INACTIVE状态
                        updatedRecord.setStatus(PmiRecord.PmiStatus.INACTIVE);
                        pmiRecordRepository.save(updatedRecord);
                        log.info("PMI {} 关闭窗口后无可用时长，设置为INACTIVE状态", pmiRecord.getId());
                    }
                }
            }
        } else {
            log.info("PMI {} 还有 {} 个活跃窗口，保持当前状态", pmiRecord.getId(), activeWindows.size());
        }
        
        log.info("PMI关闭成功: windowId={}, pmiNumber={}", window.getId(), pmiRecord.getPmiNumber());
    }
}
