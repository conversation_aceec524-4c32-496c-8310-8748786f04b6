package com.zoombus.service;


import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.repository.PmiScheduleWindowRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 任务执行策略判断器
 * 根据任务的调度时间和窗口状态决定执行策略
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TaskExecutionStrategyDecider {
    

    private final PmiScheduleWindowRepository windowRepository;
    
    /**
     * 执行策略枚举
     */
    public enum ExecutionStrategy {
        IMMEDIATE,    // 立即执行
        SCHEDULED,    // 调度执行
        EXPIRED       // 已过期
    }
    
    /**
     * 决定任务执行策略
     * 
     * @param windowId 窗口ID
     * @param scheduledTime 计划执行时间
     * @param taskType 任务类型
     * @return 执行策略
     */
    public ExecutionStrategy decideStrategy(Long windowId, 
                                          LocalDateTime scheduledTime, 
                                          PmiScheduleWindowTask.TaskType taskType) {
        // 立即执行机制始终启用
        
        try {
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(windowId);
            if (!windowOpt.isPresent()) {
                log.warn("PMI窗口不存在，使用调度执行: windowId={}", windowId);
                return ExecutionStrategy.SCHEDULED;
            }
            
            PmiScheduleWindow window = windowOpt.get();
            return decideStrategy(scheduledTime, taskType, window);
            
        } catch (Exception e) {
            log.error("判断执行策略失败，降级到调度执行: windowId={}", windowId, e);
            return ExecutionStrategy.SCHEDULED;
        }
    }
    
    /**
     * 决定任务执行策略（内部方法）
     */
    private ExecutionStrategy decideStrategy(LocalDateTime scheduledTime, 
                                           PmiScheduleWindowTask.TaskType taskType,
                                           PmiScheduleWindow window) {
        LocalDateTime now = LocalDateTime.now();
        long minutesDiff = Duration.between(scheduledTime, now).toMinutes();
        
        log.debug("判断执行策略: scheduledTime={}, now={}, diff={}分钟, taskType={}", 
                scheduledTime, now, minutesDiff, taskType);
        
        // 过往时间：检查是否在有效期内
        if (scheduledTime.isBefore(now)) {
            return handlePastTime(scheduledTime, taskType, window, now, minutesDiff);
        }
        
        // 当前时间（容差范围内）：立即执行（固定2分钟容差）
        if (Math.abs(minutesDiff) <= 2) {
            log.info("任务在时间容差范围内，立即执行: diff={}分钟, tolerance=2分钟",
                    Math.abs(minutesDiff));
            return ExecutionStrategy.IMMEDIATE;
        }
        
        // 未来时间：调度执行
        log.debug("任务为未来时间，使用调度执行: scheduledTime={}", scheduledTime);
        return ExecutionStrategy.SCHEDULED;
    }
    
    /**
     * 处理过往时间的任务
     */
    private ExecutionStrategy handlePastTime(LocalDateTime scheduledTime,
                                           PmiScheduleWindowTask.TaskType taskType,
                                           PmiScheduleWindow window,
                                           LocalDateTime now,
                                           long delayMinutes) {
        // 过往时间任务执行始终启用

        // 根据任务类型判断是否仍然有效
        if (taskType == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
            return handlePastOpenTask(scheduledTime, window, now, delayMinutes);
        } else {
            return handlePastCloseTask(scheduledTime, window, now, delayMinutes);
        }
    }
    
    /**
     * 处理过往的开启任务
     */
    private ExecutionStrategy handlePastOpenTask(LocalDateTime scheduledTime,
                                               PmiScheduleWindow window,
                                               LocalDateTime now,
                                               long delayMinutes) {
        // 开启任务：只要当前时间未超过窗口结束时间就正常执行
        // PMI计划可能是当日0点开始的整日计划，开启task执行时间早于安排时间是正常的
        if (window.getEndDateTime().isAfter(now)) {
            log.info("开启任务仍在窗口有效期内，立即执行: scheduledTime={}, currentTime={}, windowEnd={}",
                    scheduledTime, now, window.getEndDateTime());
            return ExecutionStrategy.IMMEDIATE;
        } else {
            log.warn("开启任务已超过窗口关闭时间，标记为过期: scheduledTime={}, currentTime={}, windowEnd={}",
                    scheduledTime, now, window.getEndDateTime());
            return ExecutionStrategy.EXPIRED;
        }
    }
    
    /**
     * 处理过往的关闭任务
     * 只需要考虑PMI当前windows id仍然是本task对应的windows
     */
    private ExecutionStrategy handlePastCloseTask(LocalDateTime scheduledTime,
                                                PmiScheduleWindow window,
                                                LocalDateTime now,
                                                long delayMinutes) {
        // 关闭任务：只要窗口仍然有效就立即执行
        log.info("关闭任务仍然有效，立即执行: delayMinutes={}", delayMinutes);
        return ExecutionStrategy.IMMEDIATE;
    }
    
    /**
     * 检查窗口是否仍然有效
     */
    public boolean isWindowStillValid(Long windowId, PmiScheduleWindowTask.TaskType taskType) {
        try {
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(windowId);
            if (!windowOpt.isPresent()) {
                return false;
            }
            
            PmiScheduleWindow window = windowOpt.get();
            LocalDateTime now = LocalDateTime.now();
            
            if (taskType == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
                // 开启任务：窗口还没结束
                return window.getEndDateTime().isAfter(now);
            } else {
                // 关闭任务：窗口已经开始（避免在窗口开始前就关闭）
                return window.getStartDateTime().isBefore(now) || 
                       window.getStartDateTime().isEqual(now);
            }
            
        } catch (Exception e) {
            log.error("检查窗口有效性失败: windowId={}", windowId, e);
            return false;
        }
    }
    
    /**
     * 获取任务延迟信息
     */
    public TaskDelayInfo getTaskDelayInfo(LocalDateTime scheduledTime) {
        LocalDateTime now = LocalDateTime.now();
        long delayMinutes = Duration.between(scheduledTime, now).toMinutes();
        
        TaskDelayInfo info = new TaskDelayInfo();
        info.setScheduledTime(scheduledTime);
        info.setCurrentTime(now);
        info.setDelayMinutes(delayMinutes);
        info.setIsDelayed(delayMinutes > 0);
        info.setIsWithinTolerance(Math.abs(delayMinutes) <= 2);
        info.setIsWithinMaxDelay(true); // 不再限制最大延迟时间
        
        return info;
    }
    
    /**
     * 任务延迟信息
     */
    public static class TaskDelayInfo {
        private LocalDateTime scheduledTime;
        private LocalDateTime currentTime;
        private long delayMinutes;
        private boolean isDelayed;
        private boolean isWithinTolerance;
        private boolean isWithinMaxDelay;
        
        // Getters and Setters
        public LocalDateTime getScheduledTime() { return scheduledTime; }
        public void setScheduledTime(LocalDateTime scheduledTime) { this.scheduledTime = scheduledTime; }
        
        public LocalDateTime getCurrentTime() { return currentTime; }
        public void setCurrentTime(LocalDateTime currentTime) { this.currentTime = currentTime; }
        
        public long getDelayMinutes() { return delayMinutes; }
        public void setDelayMinutes(long delayMinutes) { this.delayMinutes = delayMinutes; }
        
        public boolean getIsDelayed() { return isDelayed; }
        public void setIsDelayed(boolean isDelayed) { this.isDelayed = isDelayed; }
        
        public boolean getIsWithinTolerance() { return isWithinTolerance; }
        public void setIsWithinTolerance(boolean isWithinTolerance) { this.isWithinTolerance = isWithinTolerance; }
        
        public boolean getIsWithinMaxDelay() { return isWithinMaxDelay; }
        public void setIsWithinMaxDelay(boolean isWithinMaxDelay) { this.isWithinMaxDelay = isWithinMaxDelay; }
    }
}
