package com.zoombus.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * PMI任务调度配置
 */
@Component
@ConfigurationProperties(prefix = "pmi.task.scheduling")
@Data
public class PmiTaskSchedulingConfig {
    
    /**
     * 是否启用新的精准定时任务机制
     * true: 使用新的事件驱动精准调度机制
     * false: 使用原有的轮询机制
     */
    private boolean enablePreciseScheduling = false;
    
    /**
     * 是否启用任务监控
     */
    private boolean enableTaskMonitoring = true;
    
    /**
     * 任务清理配置
     */
    private TaskCleanup cleanup = new TaskCleanup();
    
    /**
     * 任务重试配置
     */
    private TaskRetry retry = new TaskRetry();
    
    /**
     * 性能配置
     */
    private Performance performance = new Performance();
    
    @Data
    public static class TaskCleanup {
        /**
         * 是否启用自动清理
         */
        private boolean enabled = true;
        
        /**
         * 保留已完成任务的天数
         */
        private int retentionDays = 30;
        
        /**
         * 清理任务执行时间（cron表达式）
         */
        private String cronExpression = "0 0 2 * * ?"; // 每天凌晨2点执行
    }
    
    @Data
    public static class TaskRetry {
        /**
         * 最大重试次数
         */
        private int maxRetryCount = 3;
        
        /**
         * 重试间隔（分钟）
         */
        private int retryIntervalMinutes = 5;
        
        /**
         * 是否启用指数退避
         */
        private boolean exponentialBackoff = true;
    }
    
    @Data
    public static class Performance {
        /**
         * 批量处理大小
         */
        private int batchSize = 100;

        /**
         * 线程池大小
         */
        private int threadPoolSize = 15;



        /**
         * 是否启用任务缓存
         */
        private boolean enableTaskCache = true;

        /**
         * 缓存过期时间（分钟）
         */
        private int cacheExpirationMinutes = 10;
    }

    /**
     * 立即执行配置
     */
    private ImmediateExecution immediateExecution = new ImmediateExecution();

    /**
     * 监控配置
     */
    private Monitoring monitoring = new Monitoring();

    @Data
    public static class ImmediateExecution {
        /**
         * 是否启用立即执行机制
         */
        private boolean enabled = true;

        /**
         * 时间容差（分钟）
         */
        private int toleranceMinutes = 2;





        /**
         * 线程池配置
         */
        private ThreadPool threadPool = new ThreadPool();

        @Data
        public static class ThreadPool {
            /**
             * 核心线程数
             */
            private int coreSize = 8;

            /**
             * 最大线程数
             */
            private int maxSize = 15;

            /**
             * 队列容量
             */
            private int queueCapacity = 150;

            /**
             * 线程名前缀
             */
            private String threadNamePrefix = "immediate-task-";

            /**
             * 线程保活时间（秒）
             */
            private int keepAliveSeconds = 60;

            /**
             * 是否允许核心线程超时
             */
            private boolean allowCoreThreadTimeOut = false;
        }
    }

    @Data
    public static class Monitoring {
        /**
         * 是否启用监控
         */
        private boolean enabled = true;

        /**
         * 检查长时间SCHEDULED状态任务的间隔（毫秒）
         */
        private long stuckTaskCheckIntervalMs = 120000; // 2分钟

        /**
         * 健康检查间隔（毫秒）
         */
        private long healthCheckIntervalMs = 600000; // 10分钟



        /**
         * 指标收集间隔（秒）
         */
        private int metricsIntervalSeconds = 60;

        /**
         * 是否记录执行详情
         */
        private boolean recordExecutionDetails = true;

        /**
         * 告警配置
         */
        private Alert alert = new Alert();

        @Data
        public static class Alert {
            /**
             * 是否启用告警
             */
            private boolean enabled = true;

            /**
             * 失败率阈值（百分比）
             */
            private double failureRateThreshold = 5.0;

            /**
             * 过期任务数量阈值（每小时）
             */
            private int expiredTaskThreshold = 10;

            /**
             * 平均执行时间阈值（秒）
             */
            private int avgExecutionTimeThreshold = 5;

            /**
             * 队列满阈值（百分比）
             */
            private double queueFullThreshold = 80.0;
        }
    }
}
