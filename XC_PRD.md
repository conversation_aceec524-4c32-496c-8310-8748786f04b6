# 香仓危化品运输平台银行端对接系统 PRD

## 📋 文档信息

| 项目 | 信息 |
|------|------|
| **产品名称** | 香仓危化品运输平台银行端对接系统 |
| **项目代号** | XC-Bank-Integration |
| **版本** | v1.0 |
| **文档版本** | 1.0 |
| **创建日期** | 2025-08-29 |
| **最后更新** | 2025-08-29 |
| **产品经理** | 银行科技研发部门 |
| **合作方** | 张家港香仓鸿润贸易有限公司 |

---

## 🎯 项目概述

### 项目背景
张家港香仓鸿润贸易为企业自身搭建的危化品运输平台，需要进行运输车辆租赁双方租车保证金的资金监管服务。该平台连接货主、承运方（车主），通过保证金机制确保运输服务的可靠性和安全性。

### 业务价值
- **风险控制**: 通过保证金机制降低运输违约风险
- **资金安全**: 银行级资金监管确保交易安全
- **业务增长**: 为银行拓展B2B平台金融服务
- **合规保障**: 符合危化品运输行业监管要求

### 核心服务
- **APIBank收单服务**: 支持多种支付方式的资金收取
- **资金监管服务**: 基于子账户的保证金管理
- **资金划转服务**: 自动化的资金结算和划转
- **风控服务**: 交易风险监控和异常处理

### 目标用户
- **香仓平台**: 危化品运输平台运营方
- **货主**: 需要运输服务的企业客户
- **车主**: 提供运输服务的承运方
- **银行**: 提供金融服务的银行机构

---

## 🔄 业务流程

### 整体业务流程
```mermaid
graph TD
    A[货主登录] --> B[发布货源]
    B --> C[支付保证金]
    C --> D[等待接单]
    
    E[车主登录] --> F[查看货源]
    F --> G[接单]
    G --> H[支付保证金]
    
    D --> I{有车主接单?}
    I -->|是| J[确认接单]
    I -->|否| K[超时退款]
    
    H --> J
    J --> L[开始运输]
    L --> M[运输中]
    M --> N[送达确认]
    
    N --> O{运输完成?}
    O -->|正常完成| P[解冻保证金]
    O -->|违约| Q[扣除保证金]
    
    P --> R[资金结算]
    Q --> S[违约处理]
```

### 资金监管流程
```mermaid
graph TD
    A[开立子账户] --> B[保证金收取]
    B --> C[资金冻结]
    C --> D[运输服务]
    D --> E{服务完成?}
    E -->|正常| F[解冻保证金]
    E -->|违约| G[扣除保证金]
    F --> H[资金退还]
    G --> I[违约金处理]
```

---

## 🏗️ 系统架构

### 技术架构
```
┌─────────────────┐    ┌─────────────────┐
│   香仓平台       │    │   银行核心系统   │
│   (业务系统)     │    │   (资金处理)     │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌─────────────────┐
         │   银行API网关    │
         │  (接口服务层)    │
         └─────────────────┘
                     │
         ┌─────────────────┐
         │   子账户系统     │
         │  (资金监管)      │
         └─────────────────┘
```

### 部署架构
- **生产环境**: 银行数据中心
- **灾备环境**: 异地灾备中心  
- **网络隔离**: DMZ区域部署API网关
- **数据库**: 银行核心数据库集群
- **监控**: 7×24小时实时监控

---

## 🎨 功能需求

### 1. 账户管理模块

#### 1.1 子账户开立
- **功能描述**: 为货主、车主开立资金监管子账户
- **业务规则**: 
  - 一个客户可开立多个子账户
  - 账户状态：正常、冻结、注销
  - 支持批量开户
- **接口**: `/api/account/open`

#### 1.2 账户查询
- **功能描述**: 查询子账户信息和余额
- **查询维度**: 账户号、客户号、状态
- **接口**: `/api/account/query`

#### 1.3 账户状态管理
- **功能描述**: 账户冻结、解冻、注销
- **操作权限**: 平台方和银行方
- **接口**: `/api/account/status`

### 2. 资金监管模块

#### 2.1 保证金收取
- **功能描述**: 收取货主、车主保证金
- **支付方式**: 网银、快捷支付、扫码支付
- **金额限制**: 单笔最高10万元
- **接口**: `/api/deposit/collect`

#### 2.2 资金冻结/解冻
- **功能描述**: 根据业务状态冻结或解冻资金
- **冻结类型**: 全额冻结、部分冻结
- **解冻条件**: 运输完成、违约处理
- **接口**: `/api/funds/freeze`, `/api/funds/unfreeze`

#### 2.3 资金划转
- **功能描述**: 保证金退还、违约金扣除
- **划转类型**: 同名划转、第三方划转
- **到账时间**: T+0实时到账
- **接口**: `/api/funds/transfer`

### 3. 交易处理模块

#### 3.1 收单服务
- **功能描述**: 处理各类支付请求
- **支持渠道**: 网银、手机银行、第三方支付
- **交易限额**: 根据客户等级设定
- **接口**: `/api/payment/order`

#### 3.2 交易查询
- **功能描述**: 查询交易状态和详情
- **查询条件**: 订单号、时间范围、状态
- **接口**: `/api/payment/query`

#### 3.3 交易对账
- **功能描述**: 提供日终对账文件
- **对账内容**: 交易流水、资金变动
- **文件格式**: CSV、Excel
- **接口**: `/api/reconciliation/download`

---

## 🔌 API设计规范

### 接口认证
- **认证方式**: API Key + 数字签名
- **签名算法**: RSA-SHA256
- **时间戳**: 防重放攻击
- **IP白名单**: 限制访问来源

### 请求格式
```json
{
  "header": {
    "apiKey": "string",
    "timestamp": "string", 
    "signature": "string",
    "requestId": "string"
  },
  "body": {
    // 业务参数
  }
}
```

### 响应格式
```json
{
  "code": "string",
  "message": "string",
  "data": {},
  "timestamp": "string",
  "requestId": "string"
}
```

### 核心API接口

#### 子账户开立
```
POST /api/account/open
Content-Type: application/json

{
  "customerId": "客户号",
  "customerName": "客户名称", 
  "accountType": "账户类型",
  "businessType": "业务类型"
}
```

#### 保证金收取
```
POST /api/deposit/collect
Content-Type: application/json

{
  "orderId": "订单号",
  "accountId": "账户号",
  "amount": "金额",
  "currency": "币种",
  "purpose": "用途"
}
```

#### 资金冻结
```
POST /api/funds/freeze
Content-Type: application/json

{
  "accountId": "账户号",
  "amount": "冻结金额",
  "freezeReason": "冻结原因",
  "businessId": "业务单号"
}
```

---

## 📊 数据模型

### 核心实体

#### 1. 子账户实体 (SubAccount)
```sql
t_sub_accounts
├── account_id (账户号，主键)
├── customer_id (客户号)
├── customer_name (客户名称)
├── account_type (账户类型)
├── account_status (账户状态)
├── balance (账户余额)
├── frozen_amount (冻结金额)
├── created_time (开户时间)
└── updated_time (更新时间)
```

#### 2. 交易记录实体 (Transaction)
```sql
t_transactions
├── transaction_id (交易号，主键)
├── order_id (订单号)
├── account_id (账户号)
├── transaction_type (交易类型)
├── amount (交易金额)
├── currency (币种)
├── status (交易状态)
├── created_time (交易时间)
└── completed_time (完成时间)
```

#### 3. 资金冻结记录 (FreezeRecord)
```sql
t_freeze_records
├── freeze_id (冻结号，主键)
├── account_id (账户号)
├── business_id (业务单号)
├── freeze_amount (冻结金额)
├── freeze_reason (冻结原因)
├── freeze_status (冻结状态)
├── freeze_time (冻结时间)
└── unfreeze_time (解冻时间)
```

---

## 🔒 安全要求

### 数据安全
- **传输加密**: HTTPS + TLS 1.3
- **数据加密**: AES-256加密存储
- **敏感信息**: 脱敏处理和访问控制
- **数据备份**: 定期备份和异地存储

### 访问控制
- **身份认证**: 双因子认证
- **权限管理**: 基于角色的访问控制
- **操作审计**: 完整的操作日志记录
- **IP限制**: 白名单机制

### 风险控制
- **交易限额**: 单笔、单日、单月限额
- **异常监控**: 实时交易监控和告警
- **反洗钱**: AML合规检查
- **欺诈检测**: 智能风控模型

---

## 📈 监控运维

### 系统监控
- **可用性**: 99.9%服务可用性
- **响应时间**: API响应时间 < 3秒
- **并发处理**: 支持1000TPS并发
- **错误率**: 系统错误率 < 0.1%

### 业务监控  
- **交易成功率**: > 99.5%
- **资金安全**: 零资金损失
- **对账准确率**: 100%准确对账
- **客户满意度**: > 95%满意度

### 运维要求
- **7×24监控**: 全天候系统监控
- **故障响应**: 5分钟内响应
- **数据备份**: 每日备份
- **灾备切换**: 30分钟内完成

---

## 🚀 项目计划

### 第一阶段 (4周) - 基础功能
- ✅ 需求分析和系统设计
- ⏳ 子账户管理功能开发
- ⏳ 基础API接口开发
- ⏳ 安全认证机制实现

### 第二阶段 (4周) - 核心功能  
- ⏳ 资金监管功能开发
- ⏳ 收单服务集成
- ⏳ 交易处理功能
- ⏳ 系统测试和调试

### 第三阶段 (2周) - 集成测试
- ⏳ 与香仓平台联调测试
- ⏳ 压力测试和性能优化
- ⏳ 安全测试和漏洞修复
- ⏳ 用户验收测试

### 第四阶段 (2周) - 上线部署
- ⏳ 生产环境部署
- ⏳ 数据迁移和初始化
- ⏳ 监控系统配置
- ⏳ 运维文档编写

---

## 💼 业务场景详解

### 场景1: 货主发布货源
1. **货源发布**: 货主在香仓平台发布运输需求
2. **保证金缴纳**: 系统要求货主缴纳保证金（支付保证金）
3. **资金冻结**: 银行收取保证金并冻结在监管账户
4. **等待接单**: 货源进入待接单状态

### 场景2: 车主接单运输
1. **查看货源**: 车主浏览可接单的货源信息
2. **接单申请**: 车主提交接单申请
3. **保证金缴纳**: 车主缴纳相应保证金
4. **双方确认**: 货主确认接单，运输合同生效

### 场景3: 正常运输完成
1. **开始运输**: 车主按约定时间开始运输
2. **运输监控**: 平台实时监控运输状态
3. **送达确认**: 货主确认货物送达
4. **保证金解冻**: 银行解冻双方保证金并退还

### 场景4: 违约处理
1. **违约识别**: 系统识别违约行为（超时、拒载等）
2. **违约认定**: 平台确认违约责任方
3. **保证金扣除**: 银行扣除违约方保证金
4. **赔偿处理**: 将扣除金额赔偿给守约方

---

## 🔧 技术实现方案

### 系统集成方案
```
香仓平台 ←→ API网关 ←→ 银行核心系统
    ↓           ↓           ↓
  业务逻辑   接口转换    资金处理
    ↓           ↓           ↓
  订单管理   安全认证    账户管理
    ↓           ↓           ↓
  状态同步   日志记录    交易记录
```

### 数据同步机制
- **实时同步**: 关键业务数据实时同步
- **批量同步**: 非关键数据定时批量同步
- **异常重试**: 失败数据自动重试机制
- **数据校验**: 双方数据一致性校验

### 容错处理
- **服务降级**: 核心服务优先保障
- **熔断机制**: 防止系统雪崩
- **重试策略**: 智能重试机制
- **补偿机制**: 业务补偿处理

---

## 📋 接口详细设计

### 4. 通知服务模块

#### 4.1 交易结果通知
```
POST /api/notify/transaction
Content-Type: application/json

{
  "notifyType": "TRANSACTION_RESULT",
  "orderId": "订单号",
  "transactionId": "交易号",
  "status": "交易状态",
  "amount": "交易金额",
  "timestamp": "通知时间"
}
```

#### 4.2 账户状态通知
```
POST /api/notify/account
Content-Type: application/json

{
  "notifyType": "ACCOUNT_STATUS",
  "accountId": "账户号",
  "oldStatus": "原状态",
  "newStatus": "新状态",
  "reason": "变更原因",
  "timestamp": "通知时间"
}
```

### 5. 查询统计模块

#### 5.1 交易流水查询
```
GET /api/query/transactions
Parameters:
- accountId: 账户号
- startDate: 开始日期
- endDate: 结束日期
- transactionType: 交易类型
- pageNum: 页码
- pageSize: 页大小
```

#### 5.2 账户余额查询
```
GET /api/query/balance
Parameters:
- accountId: 账户号
- queryDate: 查询日期
```

#### 5.3 对账文件下载
```
GET /api/reconciliation/download
Parameters:
- fileDate: 对账日期
- fileType: 文件类型(CSV/EXCEL)
```

---

## 🎯 关键业务规则

### 保证金规则
- **货主保证金**: 按货值的5-10%收取
- **车主保证金**: 按运费的20-30%收取
- **最低金额**: 单笔最低1000元
- **最高金额**: 单笔最高100000元

### 资金冻结规则
- **冻结时机**: 保证金到账后立即冻结
- **冻结期限**: 最长30天自动解冻
- **部分冻结**: 支持按比例部分冻结
- **优先级**: 违约扣款优先于正常解冻

### 结算规则
- **正常结算**: T+0实时退还保证金
- **违约结算**: T+1工作日处理违约金
- **争议处理**: 冻结资金等待仲裁结果
- **手续费**: 按交易金额收取0.1%手续费

### 风控规则
- **单日限额**: 单个账户日累计限额50万
- **异常监控**: 大额交易人工审核
- **黑名单**: 违约客户加入黑名单
- **白名单**: 优质客户提高限额

---

## 🔍 测试方案

### 功能测试
- **接口测试**: 所有API接口功能验证
- **业务流程测试**: 端到端业务流程验证
- **异常场景测试**: 各种异常情况处理
- **边界值测试**: 参数边界值验证

### 性能测试
- **压力测试**: 系统最大负载测试
- **并发测试**: 高并发场景测试
- **稳定性测试**: 长时间运行稳定性
- **容量测试**: 系统容量规划验证

### 安全测试
- **渗透测试**: 系统安全漏洞检测
- **数据安全测试**: 敏感数据保护验证
- **接口安全测试**: API安全机制验证
- **权限测试**: 访问权限控制验证

### 兼容性测试
- **浏览器兼容**: 主流浏览器兼容性
- **移动端兼容**: 手机端功能验证
- **系统兼容**: 不同操作系统兼容
- **版本兼容**: 系统版本升级兼容

---

## 📊 运营数据指标

### 业务指标
- **日交易笔数**: 目标1000笔/日
- **日交易金额**: 目标500万元/日
- **保证金规模**: 目标1000万元
- **客户数量**: 目标1000个活跃客户

### 技术指标
- **系统可用性**: 99.9%
- **接口响应时间**: 平均2秒
- **并发处理能力**: 1000TPS
- **数据准确率**: 100%

### 服务指标
- **客户满意度**: >95%
- **投诉处理时效**: 24小时内
- **问题解决率**: >98%
- **服务响应时间**: 5分钟内

---

## 📞 联系信息

### 项目信息
- **项目名称**: 香仓危化品运输平台银行端对接系统
- **项目代号**: XC-Bank-Integration
- **项目周期**: 12周
- **预算**: 待评估
- **风险等级**: 中等

### 团队信息
- **项目经理**: 银行科技研发部门
- **技术负责人**: 待指定
- **业务负责人**: 待指定
- **测试负责人**: 待指定
- **运维负责人**: 待指定

### 合作方信息
- **合作企业**: 张家港香仓鸿润贸易有限公司
- **业务联系人**: 待确认
- **技术联系人**: 待确认
- **项目对接人**: 待确认

---

*文档版本: v1.0*
*最后更新: 2025-08-29*
*状态: 需求分析阶段*
*下次更新: 根据评审结果调整*
