#!/bin/bash

echo "=== PMI Task 94 诊断报告 ==="
echo "执行时间: $(date)"
echo ""

# 1. 检查Task 94的基本信息
echo "1. 检查Task 94基本信息:"
mysql -u root -p'Nslcp@2024' zoombus -e "
SELECT 
    id, 
    pmi_window_id, 
    task_type, 
    scheduled_time, 
    actual_execution_time, 
    status, 
    retry_count, 
    error_message,
    created_at,
    updated_at
FROM t_pmi_schedule_window_tasks 
WHERE id = 94;" 2>/dev/null

echo ""

# 2. 检查对应的PMI窗口信息
echo "2. 检查PMI窗口信息:"
WINDOW_ID=$(mysql -u root -p'Nslcp@2024' zoombus -e "SELECT pmi_window_id FROM t_pmi_schedule_window_tasks WHERE id = 94;" 2>/dev/null | tail -n 1)
if [ -n "$WINDOW_ID" ] && [ "$WINDOW_ID" != "NULL" ]; then
    mysql -u root -p'Nslcp@2024' zoombus -e "
    SELECT 
        id,
        schedule_id,
        start_datetime,
        end_datetime,
        status,
        actual_start_time,
        actual_end_time,
        created_at
    FROM t_pmi_schedule_windows 
    WHERE id = $WINDOW_ID;" 2>/dev/null
else
    echo "未找到关联的PMI窗口"
fi

echo ""

# 3. 检查PMI记录信息
echo "3. 检查PMI记录信息:"
PMI_NUMBER=$(mysql -u root -p'Nslcp@2024' zoombus -e "
SELECT p.pmi_number 
FROM t_pmi_records p
JOIN t_pmi_schedules s ON p.id = s.pmi_record_id
JOIN t_pmi_schedule_windows w ON s.id = w.schedule_id
JOIN t_pmi_schedule_window_tasks t ON w.id = t.pmi_window_id
WHERE t.id = 94;" 2>/dev/null | tail -n 1)

if [ -n "$PMI_NUMBER" ] && [ "$PMI_NUMBER" != "NULL" ]; then
    mysql -u root -p'Nslcp@2024' zoombus -e "
    SELECT 
        p.id,
        p.pmi_number,
        p.status,
        p.billing_mode,
        p.remaining_duration_minutes,
        u.username
    FROM t_pmi_records p
    LEFT JOIN t_users u ON p.user_id = u.id
    WHERE p.pmi_number = '$PMI_NUMBER';" 2>/dev/null
else
    echo "未找到关联的PMI记录"
fi

echo ""

# 4. 检查相关的所有任务
echo "4. 检查窗口的所有任务:"
if [ -n "$WINDOW_ID" ] && [ "$WINDOW_ID" != "NULL" ]; then
    mysql -u root -p'Nslcp@2024' zoombus -e "
    SELECT 
        id,
        task_type,
        scheduled_time,
        actual_execution_time,
        status,
        retry_count,
        error_message
    FROM t_pmi_schedule_window_tasks 
    WHERE pmi_window_id = $WINDOW_ID
    ORDER BY id;" 2>/dev/null
fi

echo ""

# 5. 检查Redis中的任务状态
echo "5. 检查Redis中的任务状态:"
redis-cli -p 6379 GET "task:94" 2>/dev/null || echo "Redis中无task:94的记录"
redis-cli -p 6379 GET "running_tasks:task_94" 2>/dev/null || echo "Redis中无running_tasks:task_94的记录"

echo ""

# 6. 检查调度器状态
echo "6. 检查调度器状态:"
curl -s http://localhost:8080/api/scheduler-monitor/health 2>/dev/null | jq '.' || echo "调度器健康检查API调用失败"

echo ""

# 7. 检查任务监控统计
echo "7. 检查任务监控统计:"
curl -s http://localhost:8080/api/pmi-task-monitor/stats 2>/dev/null | jq '.' || echo "任务监控统计API调用失败"

echo ""

# 8. 检查系统时间
echo "8. 系统时间信息:"
echo "当前时间: $(date)"
echo "时区: $(timedatectl show --property=Timezone --value 2>/dev/null || echo 'Unknown')"

echo ""

# 9. 检查数据库连接
echo "9. 数据库连接测试:"
mysql -u root -p'Nslcp@2024' -e "SELECT NOW() as current_time, VERSION() as mysql_version;" 2>/dev/null && echo "✓ 数据库连接正常" || echo "✗ 数据库连接失败"

echo ""

# 10. 检查是否有其他相关任务
echo "10. 检查最近的PMI任务执行情况:"
mysql -u root -p'Nslcp@2024' zoombus -e "
SELECT 
    id,
    task_type,
    scheduled_time,
    actual_execution_time,
    status,
    TIMESTAMPDIFF(MINUTE, scheduled_time, NOW()) as delay_minutes
FROM t_pmi_schedule_window_tasks 
WHERE scheduled_time >= '$(date -d '1 day ago' '+%Y-%m-%d %H:%M:%S')'
ORDER BY id DESC
LIMIT 10;" 2>/dev/null

echo ""

# 11. 检查应用日志中的相关信息
echo "11. 检查应用日志中task 94的相关信息:"
if [ -f "/root/zoombus/zoombus.log" ]; then
    echo "最近的task 94相关日志:"
    grep -i "task.*94\|taskId.*94" /root/zoombus/zoombus.log | tail -10 || echo "未找到相关日志"
else
    echo "日志文件不存在"
fi

echo ""

# 12. 检查服务进程状态
echo "12. 检查服务进程状态:"
ps aux | grep zoombus-1.0.0.jar | grep -v grep || echo "zoombus服务未运行"

echo ""

# 13. 尝试手动触发任务诊断
echo "13. 尝试手动触发任务诊断:"
curl -s -X GET http://localhost:8080/api/pmi-scheduled-tasks/94/diagnose 2>/dev/null | jq '.' || echo "任务诊断API调用失败"

echo ""

# 14. 尝试手动重新调度任务
echo "14. 检查是否可以重新调度任务:"
curl -s -X POST http://localhost:8080/api/pmi-scheduled-tasks/94/reschedule 2>/dev/null | jq '.' || echo "任务重新调度API调用失败"

echo ""
echo "=== 诊断完成 ==="
echo ""
echo "建议检查项目:"
echo "1. 任务状态是否为SCHEDULED且已过期"
echo "2. PMI窗口状态是否正常"
echo "3. Redis中是否有残留的锁信息"
echo "4. 调度器是否正常运行"
echo "5. 系统资源是否充足"
echo "6. 网络连接是否正常"
