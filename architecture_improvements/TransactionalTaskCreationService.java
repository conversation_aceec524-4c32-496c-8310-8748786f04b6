package com.zoombus.service.improved;

import org.apache.rocketmq.spring.annotation.RocketMQTransactionListener;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 事务性任务创建服务
 * 解决任务创建和调度的原子性问题
 */
@Service
public class TransactionalTaskCreationService {

    private final RocketMQTemplate rocketMQTemplate;
    private final PmiScheduleWindowTaskRepository taskRepository;
    
    public TransactionalTaskCreationService(
            RocketMQTemplate rocketMQTemplate,
            PmiScheduleWindowTaskRepository taskRepository) {
        this.rocketMQTemplate = rocketMQTemplate;
        this.taskRepository = taskRepository;
    }

    /**
     * 创建任务并发送事务消息
     */
    public void createTaskWithTransactionalMessage(PmiScheduleWindow window) {
        // 构建任务消息
        TaskCreationMessage message = TaskCreationMessage.builder()
                .windowId(window.getId())
                .scheduledTime(window.getStartDateTime())
                .taskType(TaskType.PMI_WINDOW_OPEN)
                .build();

        // 发送事务消息
        rocketMQTemplate.sendMessageInTransaction(
                "task-creation-topic",
                MessageBuilder.withPayload(message)
                        .setHeader("windowId", window.getId())
                        .build(),
                window // 传递给本地事务的参数
        );
    }

    /**
     * 本地事务：创建任务记录
     */
    @RocketMQTransactionListener(txProducerGroup = "task-creation-group")
    public class TaskCreationTransactionListener implements RocketMQLocalTransactionListener {
        
        @Override
        @Transactional
        public RocketMQLocalTransactionState executeLocalTransaction(Message msg, Object arg) {
            try {
                PmiScheduleWindow window = (PmiScheduleWindow) arg;
                
                // 创建开启任务
                PmiScheduleWindowTask openTask = new PmiScheduleWindowTask();
                openTask.setPmiWindowId(window.getId());
                openTask.setTaskType(TaskType.PMI_WINDOW_OPEN);
                openTask.setScheduledTime(window.getStartDateTime());
                openTask.setStatus(TaskStatus.PENDING); // 注意：初始状态为PENDING
                
                taskRepository.save(openTask);
                
                // 创建关闭任务
                PmiScheduleWindowTask closeTask = new PmiScheduleWindowTask();
                closeTask.setPmiWindowId(window.getId());
                closeTask.setTaskType(TaskType.PMI_WINDOW_CLOSE);
                closeTask.setScheduledTime(window.getEndDateTime());
                closeTask.setStatus(TaskStatus.PENDING);
                
                taskRepository.save(closeTask);
                
                // 更新窗口关联
                window.setOpenTaskId(openTask.getId());
                window.setCloseTaskId(closeTask.getId());
                windowRepository.save(window);
                
                return RocketMQLocalTransactionState.COMMIT;
                
            } catch (Exception e) {
                log.error("本地事务执行失败", e);
                return RocketMQLocalTransactionState.ROLLBACK;
            }
        }

        @Override
        public RocketMQLocalTransactionState checkLocalTransaction(Message msg) {
            // 检查本地事务状态
            String windowId = msg.getHeaders().get("windowId").toString();
            PmiScheduleWindow window = windowRepository.findById(Long.valueOf(windowId));
            
            if (window != null && window.getOpenTaskId() != null) {
                return RocketMQLocalTransactionState.COMMIT;
            } else {
                return RocketMQLocalTransactionState.ROLLBACK;
            }
        }
    }
}
