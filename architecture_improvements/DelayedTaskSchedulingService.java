package com.zoombus.service.improved;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 延迟任务调度服务
 * 解决立即执行导致的竞态条件问题
 */
@Service
public class DelayedTaskSchedulingService {

    private final RocketMQTemplate rocketMQTemplate;
    private final DistributedTaskLockManager lockManager;
    private final TaskExecutionService executionService;
    
    // 延迟级别配置（RocketMQ支持的延迟级别）
    private static final int[] DELAY_LEVELS = {
        1, 5, 10, 30, // 1s, 5s, 10s, 30s
        60, 120, 180, 240, 300, // 1m, 2m, 3m, 4m, 5m
        360, 420, 480, 540, 600, // 6m, 7m, 8m, 9m, 10m
        1200, 1800, 3600, 7200  // 20m, 30m, 1h, 2h
    };

    public DelayedTaskSchedulingService(
            RocketMQTemplate rocketMQTemplate,
            DistributedTaskLockManager lockManager,
            TaskExecutionService executionService) {
        this.rocketMQTemplate = rocketMQTemplate;
        this.lockManager = lockManager;
        this.executionService = executionService;
    }

    /**
     * 调度任务执行
     */
    public void scheduleTask(Long taskId, LocalDateTime scheduledTime) {
        LocalDateTime now = LocalDateTime.now();
        long delaySeconds = ChronoUnit.SECONDS.between(now, scheduledTime);
        
        if (delaySeconds <= 0) {
            // 立即执行，但添加3秒缓冲时间确保事务提交
            delaySeconds = 3;
        }
        
        // 选择合适的延迟级别
        int delayLevel = selectDelayLevel(delaySeconds);
        
        TaskExecutionMessage message = TaskExecutionMessage.builder()
                .taskId(taskId)
                .scheduledTime(scheduledTime)
                .actualScheduleTime(now.plusSeconds(delaySeconds))
                .build();

        // 发送延迟消息
        rocketMQTemplate.syncSend(
                "task-execution-topic",
                MessageBuilder.withPayload(message).build(),
                3000, // 发送超时
                delayLevel
        );

        log.info("任务已调度: taskId={}, scheduledTime={}, delaySeconds={}, delayLevel={}", 
                taskId, scheduledTime, delaySeconds, delayLevel);
    }

    /**
     * 选择合适的延迟级别
     */
    private int selectDelayLevel(long delaySeconds) {
        for (int i = 0; i < DELAY_LEVELS.length; i++) {
            if (delaySeconds <= DELAY_LEVELS[i]) {
                return i + 1; // RocketMQ延迟级别从1开始
            }
        }
        return DELAY_LEVELS.length; // 使用最大延迟级别
    }

    /**
     * 消息消费者：执行任务
     */
    @RocketMQMessageListener(
            topic = "task-execution-topic",
            consumerGroup = "task-execution-consumer",
            maxReconsumeTimes = 3
    )
    public class TaskExecutionConsumer implements RocketMQListener<TaskExecutionMessage> {

        @Override
        public void onMessage(TaskExecutionMessage message) {
            Long taskId = message.getTaskId();
            
            try {
                // 使用分布式锁确保任务不被重复执行
                lockManager.executeWithLock(taskId, "EXECUTE", () -> {
                    
                    // 检查任务当前状态
                    PmiScheduleWindowTask task = taskRepository.findById(taskId)
                            .orElseThrow(() -> new TaskNotFoundException("任务不存在: " + taskId));
                    
                    // 只有PENDING或SCHEDULED状态的任务才能执行
                    if (task.getStatus() != TaskStatus.PENDING && 
                        task.getStatus() != TaskStatus.SCHEDULED) {
                        log.warn("任务状态不允许执行: taskId={}, status={}", taskId, task.getStatus());
                        return null;
                    }
                    
                    // 更新任务状态为SCHEDULED
                    if (task.getStatus() == TaskStatus.PENDING) {
                        task.setStatus(TaskStatus.SCHEDULED);
                        taskRepository.save(task);
                    }
                    
                    // 执行任务
                    executionService.executeTask(task);
                    
                    return null;
                });
                
            } catch (Exception e) {
                log.error("任务执行失败: taskId={}", taskId, e);
                // 消息会自动重试，达到最大重试次数后进入死信队列
                throw new RuntimeException("任务执行失败", e);
            }
        }
    }

    /**
     * 任务执行消息
     */
    @Data
    @Builder
    public static class TaskExecutionMessage {
        private Long taskId;
        private LocalDateTime scheduledTime;
        private LocalDateTime actualScheduleTime;
    }
}
