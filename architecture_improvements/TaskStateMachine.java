package com.zoombus.service.improved;

import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.StateMachineBuilder;
import org.springframework.stereotype.Service;
import java.util.EnumSet;

/**
 * 任务状态机管理器
 * 确保任务状态转换的正确性和原子性
 */
@Service
public class TaskStateMachine {

    /**
     * 任务状态枚举
     */
    public enum TaskState {
        PENDING,     // 待调度
        SCHEDULED,   // 已调度
        EXECUTING,   // 执行中
        SUCCESS,     // 成功
        FAILED,      // 失败
        CANCELLED,   // 已取消
        RETRYING     // 重试中
    }

    /**
     * 状态转换事件
     */
    public enum TaskEvent {
        SCHEDULE,    // 调度
        START,       // 开始执行
        COMPLETE,    // 完成
        FAIL,        // 失败
        RETRY,       // 重试
        CANCEL       // 取消
    }

    /**
     * 创建状态机
     */
    public StateMachine<TaskState, TaskEvent> createStateMachine() throws Exception {
        StateMachineBuilder.Builder<TaskState, TaskEvent> builder = 
            StateMachineBuilder.builder();

        builder.configureStates()
            .withStates()
            .initial(TaskState.PENDING)
            .states(EnumSet.allOf(TaskState.class));

        builder.configureTransitions()
            // PENDING -> SCHEDULED
            .withExternal()
                .source(TaskState.PENDING)
                .target(TaskState.SCHEDULED)
                .event(TaskEvent.SCHEDULE)
                .and()
            // SCHEDULED -> EXECUTING
            .withExternal()
                .source(TaskState.SCHEDULED)
                .target(TaskState.EXECUTING)
                .event(TaskEvent.START)
                .and()
            // EXECUTING -> SUCCESS
            .withExternal()
                .source(TaskState.EXECUTING)
                .target(TaskState.SUCCESS)
                .event(TaskEvent.COMPLETE)
                .and()
            // EXECUTING -> FAILED
            .withExternal()
                .source(TaskState.EXECUTING)
                .target(TaskState.FAILED)
                .event(TaskEvent.FAIL)
                .and()
            // FAILED -> RETRYING
            .withExternal()
                .source(TaskState.FAILED)
                .target(TaskState.RETRYING)
                .event(TaskEvent.RETRY)
                .and()
            // RETRYING -> EXECUTING
            .withExternal()
                .source(TaskState.RETRYING)
                .target(TaskState.EXECUTING)
                .event(TaskEvent.START)
                .and()
            // 任何状态 -> CANCELLED
            .withExternal()
                .source(TaskState.PENDING)
                .target(TaskState.CANCELLED)
                .event(TaskEvent.CANCEL)
                .and()
            .withExternal()
                .source(TaskState.SCHEDULED)
                .target(TaskState.CANCELLED)
                .event(TaskEvent.CANCEL);

        return builder.build();
    }

    /**
     * 验证状态转换是否合法
     */
    public boolean isValidTransition(TaskState from, TaskState to) {
        switch (from) {
            case PENDING:
                return to == TaskState.SCHEDULED || to == TaskState.CANCELLED;
            case SCHEDULED:
                return to == TaskState.EXECUTING || to == TaskState.CANCELLED;
            case EXECUTING:
                return to == TaskState.SUCCESS || to == TaskState.FAILED;
            case FAILED:
                return to == TaskState.RETRYING;
            case RETRYING:
                return to == TaskState.EXECUTING;
            case SUCCESS:
            case CANCELLED:
                return false; // 终态，不能再转换
            default:
                return false;
        }
    }

    /**
     * 获取下一个可能的状态
     */
    public Set<TaskState> getNextPossibleStates(TaskState currentState) {
        switch (currentState) {
            case PENDING:
                return Set.of(TaskState.SCHEDULED, TaskState.CANCELLED);
            case SCHEDULED:
                return Set.of(TaskState.EXECUTING, TaskState.CANCELLED);
            case EXECUTING:
                return Set.of(TaskState.SUCCESS, TaskState.FAILED);
            case FAILED:
                return Set.of(TaskState.RETRYING);
            case RETRYING:
                return Set.of(TaskState.EXECUTING);
            default:
                return Set.of(); // 终态
        }
    }
}
