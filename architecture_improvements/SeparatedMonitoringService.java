package com.zoombus.service.improved;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 分离的监控服务
 * 只负责发现问题，不直接处理
 */
@Service
public class SeparatedMonitoringService {

    private final PmiScheduleWindowTaskRepository taskRepository;
    private final TaskRetryService retryService;
    private final AlertService alertService;
    private final MetricsCollector metricsCollector;

    public SeparatedMonitoringService(
            PmiScheduleWindowTaskRepository taskRepository,
            TaskRetryService retryService,
            AlertService alertService,
            MetricsCollector metricsCollector) {
        this.taskRepository = taskRepository;
        this.retryService = retryService;
        this.alertService = alertService;
        this.metricsCollector = metricsCollector;
    }

    /**
     * 监控卡住的任务
     */
    @Scheduled(fixedDelay = 120000) // 2分钟执行一次
    public void monitorStuckTasks() {
        try {
            LocalDateTime threshold = LocalDateTime.now().minusMinutes(5);
            
            // 查找长时间处于SCHEDULED状态的任务
            List<PmiScheduleWindowTask> stuckTasks = taskRepository
                    .findByStatusAndUpdatedAtBefore(TaskStatus.SCHEDULED, threshold);

            for (PmiScheduleWindowTask task : stuckTasks) {
                handleStuckTask(task);
            }

            // 更新监控指标
            metricsCollector.recordStuckTaskCount(stuckTasks.size());

        } catch (Exception e) {
            log.error("监控卡住任务失败", e);
            alertService.sendAlert("监控服务异常", e.getMessage());
        }
    }

    /**
     * 监控失败任务
     */
    @Scheduled(fixedDelay = 300000) // 5分钟执行一次
    public void monitorFailedTasks() {
        try {
            LocalDateTime since = LocalDateTime.now().minusHours(1);
            
            // 查找最近失败的任务
            List<PmiScheduleWindowTask> failedTasks = taskRepository
                    .findByStatusAndUpdatedAtAfter(TaskStatus.FAILED, since);

            // 分析失败原因
            analyzeFailurePatterns(failedTasks);

            // 触发重试符合条件的任务
            for (PmiScheduleWindowTask task : failedTasks) {
                if (shouldRetry(task)) {
                    retryService.scheduleRetry(task);
                }
            }

            // 更新监控指标
            metricsCollector.recordFailedTaskCount(failedTasks.size());

        } catch (Exception e) {
            log.error("监控失败任务失败", e);
            alertService.sendAlert("监控服务异常", e.getMessage());
        }
    }

    /**
     * 监控系统健康状态
     */
    @Scheduled(fixedDelay = 600000) // 10分钟执行一次
    public void monitorSystemHealth() {
        try {
            SystemHealthMetrics metrics = collectSystemHealthMetrics();
            
            // 检查各项指标
            checkTaskExecutionRate(metrics);
            checkErrorRate(metrics);
            checkResourceUsage(metrics);
            
            // 记录健康指标
            metricsCollector.recordSystemHealth(metrics);

        } catch (Exception e) {
            log.error("系统健康监控失败", e);
            alertService.sendAlert("系统健康监控异常", e.getMessage());
        }
    }

    /**
     * 处理卡住的任务
     */
    private void handleStuckTask(PmiScheduleWindowTask task) {
        log.warn("发现卡住的任务: taskId={}, type={}, scheduledTime={}", 
                task.getId(), task.getTaskType(), task.getScheduledTime());

        // 记录监控事件
        MonitoringEvent event = MonitoringEvent.builder()
                .taskId(task.getId())
                .eventType(MonitoringEventType.STUCK_TASK)
                .description("任务长时间处于SCHEDULED状态")
                .detectedAt(LocalDateTime.now())
                .build();

        // 发送给重试服务处理
        retryService.handleStuckTask(task, event);

        // 更新监控指标
        metricsCollector.recordStuckTaskDetected(task.getTaskType());
    }

    /**
     * 分析失败模式
     */
    private void analyzeFailurePatterns(List<PmiScheduleWindowTask> failedTasks) {
        Map<String, Long> errorPatterns = failedTasks.stream()
                .collect(Collectors.groupingBy(
                    task -> extractErrorPattern(task.getErrorMessage()),
                    Collectors.counting()
                ));

        // 如果某种错误模式频繁出现，发送告警
        errorPatterns.forEach((pattern, count) -> {
            if (count >= 5) { // 阈值可配置
                alertService.sendAlert(
                    "频繁失败模式检测",
                    String.format("错误模式 '%s' 在1小时内出现 %d 次", pattern, count)
                );
            }
        });
    }

    /**
     * 判断是否应该重试
     */
    private boolean shouldRetry(PmiScheduleWindowTask task) {
        // 重试次数限制
        if (task.getRetryCount() >= 3) {
            return false;
        }

        // 某些错误类型不重试
        if (isNonRetryableError(task.getErrorMessage())) {
            return false;
        }

        // 任务类型特定的重试逻辑
        if (task.getTaskType() == TaskType.PMI_WINDOW_OPEN) {
            return isOpenTaskRetryable(task);
        } else if (task.getTaskType() == TaskType.PMI_WINDOW_CLOSE) {
            return isCloseTaskRetryable(task);
        }

        return true;
    }

    /**
     * 收集系统健康指标
     */
    private SystemHealthMetrics collectSystemHealthMetrics() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneHourAgo = now.minusHours(1);

        return SystemHealthMetrics.builder()
                .timestamp(now)
                .totalTasks(taskRepository.countByCreatedAtAfter(oneHourAgo))
                .successfulTasks(taskRepository.countByStatusAndUpdatedAtAfter(TaskStatus.SUCCESS, oneHourAgo))
                .failedTasks(taskRepository.countByStatusAndUpdatedAtAfter(TaskStatus.FAILED, oneHourAgo))
                .pendingTasks(taskRepository.countByStatus(TaskStatus.PENDING))
                .scheduledTasks(taskRepository.countByStatus(TaskStatus.SCHEDULED))
                .executingTasks(taskRepository.countByStatus(TaskStatus.EXECUTING))
                .build();
    }
}
