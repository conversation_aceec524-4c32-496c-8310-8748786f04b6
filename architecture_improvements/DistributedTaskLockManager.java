package com.zoombus.service.improved;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import java.util.concurrent.TimeUnit;

/**
 * 分布式任务锁管理器
 * 解决并发执行同一任务的问题
 */
@Service
public class DistributedTaskLockManager {

    private final RedissonClient redissonClient;
    private static final String TASK_LOCK_PREFIX = "task:lock:";
    private static final long LOCK_WAIT_TIME = 5; // 等待锁的时间（秒）
    private static final long LOCK_LEASE_TIME = 30; // 锁的租期（秒）

    public DistributedTaskLockManager(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * 执行带锁的任务
     */
    public <T> T executeWithLock(Long taskId, String operation, TaskExecutor<T> executor) {
        String lockKey = TASK_LOCK_PREFIX + taskId + ":" + operation;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            // 尝试获取锁
            boolean acquired = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (!acquired) {
                throw new TaskLockException("无法获取任务锁: taskId=" + taskId + ", operation=" + operation);
            }
            
            log.info("获取任务锁成功: taskId={}, operation={}", taskId, operation);
            
            // 执行任务
            return executor.execute();
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new TaskLockException("获取锁被中断: taskId=" + taskId, e);
        } finally {
            // 释放锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("释放任务锁: taskId={}, operation={}", taskId, operation);
            }
        }
    }

    /**
     * 检查任务是否被锁定
     */
    public boolean isTaskLocked(Long taskId, String operation) {
        String lockKey = TASK_LOCK_PREFIX + taskId + ":" + operation;
        RLock lock = redissonClient.getLock(lockKey);
        return lock.isLocked();
    }

    /**
     * 强制释放任务锁（仅在异常情况下使用）
     */
    public void forceUnlock(Long taskId, String operation) {
        String lockKey = TASK_LOCK_PREFIX + taskId + ":" + operation;
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.isLocked()) {
            lock.forceUnlock();
            log.warn("强制释放任务锁: taskId={}, operation={}", taskId, operation);
        }
    }

    @FunctionalInterface
    public interface TaskExecutor<T> {
        T execute() throws Exception;
    }

    public static class TaskLockException extends RuntimeException {
        public TaskLockException(String message) {
            super(message);
        }
        
        public TaskLockException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
