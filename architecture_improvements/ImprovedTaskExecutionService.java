package com.zoombus.service.improved;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 改进的任务执行服务
 * 增强幂等性、状态管理和错误处理
 */
@Service
public class ImprovedTaskExecutionService {

    private final PmiScheduleWindowTaskRepository taskRepository;
    private final PmiScheduleWindowRepository windowRepository;
    private final TaskStateMachine stateMachine;
    private final TaskExecutionRecordRepository executionRecordRepository;
    private final ZoomApiService zoomApiService;

    public ImprovedTaskExecutionService(
            PmiScheduleWindowTaskRepository taskRepository,
            PmiScheduleWindowRepository windowRepository,
            TaskStateMachine stateMachine,
            TaskExecutionRecordRepository executionRecordRepository,
            ZoomApiService zoomApiService) {
        this.taskRepository = taskRepository;
        this.windowRepository = windowRepository;
        this.stateMachine = stateMachine;
        this.executionRecordRepository = executionRecordRepository;
        this.zoomApiService = zoomApiService;
    }

    /**
     * 执行任务（幂等性保证）
     */
    @Transactional
    public TaskExecutionResult executeTask(PmiScheduleWindowTask task) {
        String executionId = UUID.randomUUID().toString();
        
        try {
            // 1. 检查是否已经执行过（幂等性）
            if (isTaskAlreadyExecuted(task.getId())) {
                log.info("任务已执行过，跳过: taskId={}", task.getId());
                return TaskExecutionResult.alreadyExecuted(task.getId());
            }

            // 2. 创建执行记录
            TaskExecutionRecord record = createExecutionRecord(task, executionId);

            // 3. 更新任务状态为执行中
            updateTaskStatus(task, TaskStateMachine.TaskState.EXECUTING, executionId);

            // 4. 执行具体业务逻辑
            TaskExecutionResult result = executeBusinessLogic(task, record);

            // 5. 更新最终状态
            TaskStateMachine.TaskState finalState = result.isSuccess() ? 
                TaskStateMachine.TaskState.SUCCESS : TaskStateMachine.TaskState.FAILED;
            updateTaskStatus(task, finalState, executionId);

            // 6. 更新执行记录
            updateExecutionRecord(record, result);

            return result;

        } catch (Exception e) {
            log.error("任务执行异常: taskId={}, executionId={}", task.getId(), executionId, e);
            
            // 更新任务状态为失败
            updateTaskStatus(task, TaskStateMachine.TaskState.FAILED, executionId);
            
            return TaskExecutionResult.failed(task.getId(), e.getMessage());
        }
    }

    /**
     * 检查任务是否已执行
     */
    private boolean isTaskAlreadyExecuted(Long taskId) {
        return executionRecordRepository.existsByTaskIdAndStatus(taskId, ExecutionStatus.SUCCESS);
    }

    /**
     * 创建执行记录
     */
    private TaskExecutionRecord createExecutionRecord(PmiScheduleWindowTask task, String executionId) {
        TaskExecutionRecord record = TaskExecutionRecord.builder()
                .taskId(task.getId())
                .executionId(executionId)
                .taskType(task.getTaskType())
                .startTime(LocalDateTime.now())
                .status(ExecutionStatus.RUNNING)
                .build();
        
        return executionRecordRepository.save(record);
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(PmiScheduleWindowTask task, 
                                 TaskStateMachine.TaskState newState, 
                                 String executionId) {
        // 验证状态转换
        TaskStateMachine.TaskState currentState = convertToStateMachineState(task.getStatus());
        if (!stateMachine.isValidTransition(currentState, newState)) {
            throw new IllegalStateException(
                String.format("非法状态转换: taskId=%d, from=%s, to=%s", 
                    task.getId(), currentState, newState));
        }

        // 更新状态
        task.setStatus(convertToTaskStatus(newState));
        task.setLastExecutionId(executionId);
        task.setUpdatedAt(LocalDateTime.now());
        
        if (newState == TaskStateMachine.TaskState.EXECUTING) {
            task.setActualExecutionTime(LocalDateTime.now());
        }

        taskRepository.save(task);
        
        log.info("任务状态已更新: taskId={}, status={}, executionId={}", 
                task.getId(), newState, executionId);
    }

    /**
     * 执行具体业务逻辑
     */
    private TaskExecutionResult executeBusinessLogic(PmiScheduleWindowTask task, 
                                                   TaskExecutionRecord record) {
        if (task.getTaskType() == TaskType.PMI_WINDOW_OPEN) {
            return executeOpenTask(task, record);
        } else if (task.getTaskType() == TaskType.PMI_WINDOW_CLOSE) {
            return executeCloseTask(task, record);
        } else {
            throw new UnsupportedOperationException("不支持的任务类型: " + task.getTaskType());
        }
    }

    /**
     * 执行开启任务
     */
    private TaskExecutionResult executeOpenTask(PmiScheduleWindowTask task, 
                                              TaskExecutionRecord record) {
        try {
            PmiScheduleWindow window = windowRepository.findById(task.getPmiWindowId())
                    .orElseThrow(() -> new EntityNotFoundException("窗口不存在: " + task.getPmiWindowId()));

            // 检查窗口是否已过期
            if (LocalDateTime.now().isAfter(window.getEndDateTime())) {
                return TaskExecutionResult.failed(task.getId(), "窗口已过期");
            }

            // 调用Zoom API开启PMI
            ZoomApiResult apiResult = zoomApiService.enablePmiWindow(window);
            
            if (apiResult.isSuccess()) {
                // 更新窗口状态
                window.setStatus(WindowStatus.ACTIVE);
                window.setActualStartTime(LocalDateTime.now());
                windowRepository.save(window);
                
                return TaskExecutionResult.success(task.getId(), "PMI窗口开启成功");
            } else {
                return TaskExecutionResult.failed(task.getId(), 
                    "Zoom API调用失败: " + apiResult.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("开启任务执行失败: taskId={}", task.getId(), e);
            return TaskExecutionResult.failed(task.getId(), e.getMessage());
        }
    }

    /**
     * 执行关闭任务
     */
    private TaskExecutionResult executeCloseTask(PmiScheduleWindowTask task, 
                                               TaskExecutionRecord record) {
        // 实现关闭逻辑...
        return TaskExecutionResult.success(task.getId(), "PMI窗口关闭成功");
    }

    /**
     * 更新执行记录
     */
    private void updateExecutionRecord(TaskExecutionRecord record, TaskExecutionResult result) {
        record.setEndTime(LocalDateTime.now());
        record.setStatus(result.isSuccess() ? ExecutionStatus.SUCCESS : ExecutionStatus.FAILED);
        record.setResultMessage(result.getMessage());
        record.setErrorDetails(result.getErrorDetails());
        
        executionRecordRepository.save(record);
    }
}
