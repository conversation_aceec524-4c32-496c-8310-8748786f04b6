import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';
import './utils/traceUtils'; // 初始化TraceId工具
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import UserList from './pages/UserList';

import MeetingList from './pages/MeetingList';
import WebhookEvents from './pages/WebhookEvents';
import AdminUserList from './pages/AdminUserList';
import ZoomAuthManagement from './pages/ZoomAuthManagement';
import ZoomUserManagement from './pages/ZoomUserManagement';
import PmiManagement from './pages/PmiManagement';
import PmiScheduleManagement from './pages/PmiScheduleManagement';
import ZoomMeetingDashboard from './pages/ZoomMeetingDashboard';
import PmiBillingManagement from './pages/PmiBillingManagement';
import ZoomApiLogs from './pages/ZoomApiLogs';
import PmiReports from './pages/PmiReports';
import VersionManagement from './pages/VersionManagement';
import DatabaseMigration from './pages/DatabaseMigration';
import ScheduledTaskManagement from './pages/ScheduledTaskManagement';
import NetworkEnvironment from './pages/NetworkEnvironment';
import PmiTaskMonitor from './pages/PmiTaskMonitor';
import PmiTaskManagement from './pages/PmiTaskManagement';
import LogSearch from './pages/LogSearch';

// Join Account Rental 页面
import JoinAccountSystemConfig from './pages/JoinAccountSystemConfig';
import JoinAccountTokenManagement from './pages/JoinAccountTokenManagement';
import JoinAccountWindowQuery from './pages/JoinAccountWindowQuery';
import JoinAccountPasswordLogs from './pages/JoinAccountPasswordLogs';

import SessionExpireTest from './pages/SessionExpireTest';
import PmiUsage from './pages/PmiUsage';
import MobileDebug from './pages/MobileDebug';
import ProtectedRoute from './components/ProtectedRoute';
import AppLayout from './components/Layout';

const App = () => {
  // 禁用移动端下拉刷新
  useEffect(() => {
    // 禁用浏览器默认的下拉刷新行为
    const preventPullToRefresh = (e) => {
      // 检查是否在页面顶部
      if (window.scrollY === 0 && e.touches && e.touches.length === 1) {
        // 如果是向下滑动，阻止默认行为
        const touch = e.touches[0];
        if (touch.clientY > touch.pageY) {
          e.preventDefault();
        }
      }
    };

    // 添加事件监听器
    document.addEventListener('touchstart', preventPullToRefresh, { passive: false });
    document.addEventListener('touchmove', preventPullToRefresh, { passive: false });

    // 设置CSS属性禁用下拉刷新
    document.body.style.overscrollBehavior = 'none';
    document.documentElement.style.overscrollBehavior = 'none';

    return () => {
      // 清理事件监听器
      document.removeEventListener('touchstart', preventPullToRefresh);
      document.removeEventListener('touchmove', preventPullToRefresh);
    };
  }, []);

  return (
    <ErrorBoundary>
      <Router>
        <Routes>
        {/* 登录页面 */}
        <Route path="/login" element={<Login />} />

        {/* 受保护的路由 */}
        <Route path="/" element={
          <ProtectedRoute>
            <AppLayout />
          </ProtectedRoute>
        }>
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="users" element={<UserList />} />
          <Route path="users/:userId" element={<UserList />} />

          <Route path="meetings" element={<MeetingList />} />
          <Route path="meeting/:meetingUuid" element={<MeetingList />} />
          <Route path="webhook-events" element={<WebhookEvents />} />
          <Route path="admin-users" element={<AdminUserList />} />
          <Route path="zoom-auth" element={<ZoomAuthManagement />} />
          <Route path="zoom-users" element={<ZoomUserManagement />} />
          <Route path="zoom-users/user/:userId" element={<ZoomUserManagement />} />
          <Route path="zoom-users/:zoomUserId" element={<ZoomUserManagement />} />
          <Route path="pmi-management" element={<PmiManagement />} />
          <Route path="pmi-management/:pmiRecordId" element={<PmiManagement />} />
          <Route path="pmi-management/user/:userId" element={<PmiManagement />} />
          <Route path="pmi-management/user/:userId/create" element={<PmiManagement />} />
          <Route path="pmi-schedule-management/:pmiRecordId" element={<PmiScheduleManagement />} />
          <Route path="zoom-meeting-dashboard" element={<ZoomMeetingDashboard />} />
          <Route path="pmi-billing-management" element={<PmiBillingManagement />} />
          <Route path="zoom-api-logs" element={<ZoomApiLogs />} />
          <Route path="version-management" element={<VersionManagement />} />
          <Route path="database-migration" element={<DatabaseMigration />} />
          <Route path="scheduled-task-management" element={<ScheduledTaskManagement />} />
          <Route path="network-environment" element={<NetworkEnvironment />} />
          <Route path="log-search" element={<LogSearch />} />
          <Route path="pmi-reports" element={<PmiReports />} />
          <Route path="pmi-task-monitor" element={<PmiTaskMonitor />} />
          <Route path="pmi-task-management" element={<PmiTaskManagement />} />

          {/* Join Account Rental 路由 */}
          <Route path="join-account/system-config" element={<JoinAccountSystemConfig />} />
          <Route path="join-account/tokens" element={<JoinAccountTokenManagement />} />
          <Route path="join-account/windows" element={<JoinAccountWindowQuery />} />
          <Route path="join-account/password-logs" element={<JoinAccountPasswordLogs />} />
        </Route>

        {/* 公共路由（无需登录） */}
        <Route path="/pmi/:pmiNumber" element={<PmiUsage />} />
        <Route path="/pmi" element={<PmiUsage />} />
        <Route path="/session-expire-test" element={<SessionExpireTest />} />
        <Route path="/mobile-debug" element={<MobileDebug />} />

        {/* 默认重定向 */}
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Router>
    </ErrorBoundary>
  );
};

export default App;
