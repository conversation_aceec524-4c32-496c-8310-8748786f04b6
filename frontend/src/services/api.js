import axios from 'axios';
import { message, Modal } from 'antd';

// 创建axios实例
// 在开发环境中，直接连接到后端API；在生产环境中，使用相对路径
const baseURL = process.env.NODE_ENV === 'development' ? 'http://localhost:8080/api' : '/api';

console.log('API配置 - NODE_ENV:', process.env.NODE_ENV, 'baseURL:', baseURL);

const api = axios.create({
  baseURL: baseURL,
  timeout: 60000, // 增加到60秒用于日志搜索
});

// 防止重复弹出session过期对话框的标志位
let isShowingSessionExpiredModal = false;

// TraceId生成器
const generateTraceId = () => {
  const now = new Date();
  // 使用本地时间生成时间戳，格式：yyyyMMddHHmmssSSS
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;

  const nodeId = '001'; // 前端节点ID
  const sequence = String(Math.floor(Math.random() * 1000000)).padStart(6, '0');
  const random = Math.random().toString(36).substring(2, 8);
  return `${timestamp}-${nodeId}-${sequence}-${random}`;
};

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 为每个请求生成新的traceId
    const traceId = generateTraceId();
    sessionStorage.setItem('currentTraceId', traceId);

    config.headers['X-Trace-Id'] = traceId;

    // 在控制台显示请求信息
    console.log(`🚀 [${traceId}] ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    const traceId = response.headers['x-trace-id'] ||
                   response.config.headers['X-Trace-Id'];

    if (traceId) {
      // 更新当前traceId
      sessionStorage.setItem('currentTraceId', traceId);

      // 在控制台显示响应信息
      console.log(`✅ [${traceId}] ${response.status} ${response.config.url}`);

      // 触发traceId更新事件
      window.dispatchEvent(new CustomEvent('traceIdUpdated', {
        detail: { traceId, response }
      }));
    }

    return response;
  },
  (error) => {
    const traceId = error.response?.headers['x-trace-id'] ||
                   error.config?.headers['X-Trace-Id'];

    // 在控制台显示详细错误信息
    console.error(`❌ [${traceId}] ${error.response?.status || 'Network Error'} ${error.config?.url}`);
    console.error('错误详情:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      headers: error.response?.headers,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        baseURL: error.config?.baseURL,
        headers: error.config?.headers
      }
    });

    // 在错误消息中显示traceId
    if (traceId) {
      const originalMessage = error.response?.data?.message || error.message;
      error.userMessage = `${originalMessage} (错误码: ${traceId})`;
    }
    const { response } = error;
    if (response) {
      const { status, data } = response;
      let errorMessage = '请求失败';

      // 首先处理特殊的认证和权限错误
      switch (status) {
          case 400:
            errorMessage = '请求参数错误';
            break;
          case 401:
            // 对于401错误，也可能是session过期导致的认证问题
            // 弹出确认对话框提示用户重新登录
            if (!isShowingSessionExpiredModal) {
              isShowingSessionExpiredModal = true;
              Modal.confirm({
                title: '认证失败',
                content: '您的登录认证已失效，请重新登录',
                okText: '确认',
                cancelText: '取消',
                onOk: () => {
                  localStorage.removeItem('token');
                  localStorage.removeItem('userInfo');
                  window.location.href = '/login';
                  isShowingSessionExpiredModal = false;
                },
                onCancel: () => {
                  isShowingSessionExpiredModal = false;
                }
              });
            }
            return Promise.reject(error);
          case 403:
            // 对于403错误，可能是session过期导致的权限问题
            // 弹出确认对话框提示用户重新登录
            if (!isShowingSessionExpiredModal) {
              isShowingSessionExpiredModal = true;
              Modal.confirm({
                title: '登录已过期',
                content: '您的登录已过期，请重新登录',
                okText: '确认',
                cancelText: '取消',
                onOk: () => {
                  localStorage.removeItem('token');
                  localStorage.removeItem('userInfo');
                  window.location.href = '/login';
                  isShowingSessionExpiredModal = false;
                },
                onCancel: () => {
                  isShowingSessionExpiredModal = false;
                }
              });
            }
            return Promise.reject(error);
          case 404:
            errorMessage = '资源不存在';
            break;
          case 500:
            errorMessage = '服务器内部错误';
            break;
          default:
            // 对于其他错误，使用服务器返回的消息或默认消息
            if (data && data.message) {
              errorMessage = data.message;
            } else {
              errorMessage = `请求失败 (${status})`;
            }
        }

      if (status !== 401 && status !== 403) {
        message.error(errorMessage);
      }
    } else {
      message.error('网络连接失败');
    }

    return Promise.reject(error);
  }
);

// 用户API
export const userApi = {
  // 获取用户列表
  getUsers: (params) => api.get('/users', { params }),
  
  // 根据ID获取用户
  getUserById: (id) => api.get(`/users/${id}`),
  
  // 创建用户
  createUser: (data) => api.post('/users', data),
  
  // 更新用户
  updateUser: (id, data) => api.put(`/users/${id}`, data),
  
  // 更新用户状态
  updateUserStatus: (id, status) => api.put(`/users/${id}/status`, null, { params: { status } }),
  
  // 删除用户
  deleteUser: (id) => api.delete(`/users/${id}`),
  
  // 搜索用户
  searchUsers: (name) => api.get('/users/search', { params: { name } }),
};

// Zoom账号API已移除，改用zoomUserApi管理Zoom用户

// Zoom认证API
export const zoomAuthApi = {
  // 获取所有Zoom认证信息
  getAllZoomAuth: (params) => api.get('/zoom-auth', { params }),

  // 搜索Zoom认证信息
  searchZoomAuth: (params) => api.get('/zoom-auth/search', { params }),

  // 根据ID获取Zoom认证信息
  getZoomAuthById: (id) => api.get(`/zoom-auth/${id}`),

  // 根据ID获取Zoom认证信息用于编辑（包含敏感字段）
  getZoomAuthForEdit: (id) => api.get(`/zoom-auth/${id}/edit`),

  // 创建Zoom认证信息
  createZoomAuth: (data) => api.post('/zoom-auth', data),

  // 更新Zoom认证信息
  updateZoomAuth: (id, data) => api.put(`/zoom-auth/${id}`, data),

  // 删除Zoom认证信息
  deleteZoomAuth: (id) => api.delete(`/zoom-auth/${id}`),

  // 刷新token
  refreshToken: (id) => api.post(`/zoom-auth/${id}/refresh-token`),

  // 更新认证状态
  updateZoomAuthStatus: (id, status) => api.put(`/zoom-auth/${id}/status`, null, { params: { status } }),

  // 根据状态获取认证信息
  getZoomAuthsByStatus: (status) => api.get(`/zoom-auth/status/${status}`),

  // 同步单个ZoomAuth的子账号用户信息
  syncUsers: (id) => api.post(`/zoom-auth/${id}/sync-users`),

  // 批量同步多个ZoomAuth的子账号用户信息
  batchSyncUsers: (zoomAuthIds) => api.post('/zoom-auth/batch-sync-users', zoomAuthIds),

  // 批量刷新所有即将过期的token
  refreshAllTokens: () => api.post('/zoom-auth/refresh-all'),
};

// Zoom用户API (t_zoom_users表管理)
export const zoomUserApi = {
  // 创建Zoom用户
  createZoomUser: (data) => api.post('/zoom-users', data),

  // 更新Zoom用户
  updateZoomUser: (id, data) => api.put(`/zoom-users/${id}`, data),

  // 获取所有Zoom用户列表
  getAllZoomUsers: (params) => api.get('/zoom-users', { params }),

  // 根据ZoomAuth获取用户列表
  getZoomUsersByAuth: (zoomAuthId, params) => api.get(`/zoom-users/auth/${zoomAuthId}`, { params }),

  // 根据用户ID获取Zoom用户列表
  getZoomUsersByUserId: (userId, params) => api.get(`/zoom-users/user/${userId}`, { params }),

  // 根据ZoomUser ID获取单个Zoom用户（用于从PMI管理页面跳转）
  getZoomUserByZoomUserId: (zoomUserId, params) => api.get(`/zoom-users/zoom-user/${zoomUserId}`, { params }),

  // 根据ID获取Zoom用户
  getZoomUserById: (id) => api.get(`/zoom-users/${id}`),

  // 根据ZoomAuth和ZoomUserId获取用户
  getZoomUserByAuthAndZoomUserId: (zoomAuthId, zoomUserId) => api.get(`/zoom-users/auth/${zoomAuthId}/zoom-user/${zoomUserId}`),

  // 根据ZoomAuth和邮箱获取用户
  getZoomUserByEmail: (zoomAuthId, email) => api.get(`/zoom-users/auth/${zoomAuthId}/email/${email}`),

  // 根据状态获取用户列表
  getZoomUsersByStatus: (zoomAuthId, status) => api.get(`/zoom-users/auth/${zoomAuthId}/status/${status}`),

  // 根据用户类型获取用户列表
  getZoomUsersByUserType: (zoomAuthId, userType) => api.get(`/zoom-users/auth/${zoomAuthId}/user-type/${userType}`),

  // 根据用户类型和账号用途获取用户列表（全局搜索）
  getZoomUsersByUserTypeAndAccountUsage: (userType, accountUsage) => api.get(`/zoom-users/user-type/${userType}/account-usage/${accountUsage}`),

  // 根据用户类型和账号用途搜索用户（支持关键词搜索）
  searchZoomUsersByUserTypeAndAccountUsage: (userType, accountUsage, params) => api.get(`/zoom-users/search/user-type/${userType}/account-usage/${accountUsage}`, { params }),

  // 搜索用户（根据邮箱）
  searchZoomUsersByEmail: (zoomAuthId, email, params) => api.get(`/zoom-users/auth/${zoomAuthId}/search/email`, { params: { email, ...params } }),

  // 搜索用户（根据姓名）
  searchZoomUsersByName: (zoomAuthId, name, params) => api.get(`/zoom-users/auth/${zoomAuthId}/search/name`, { params: { name, ...params } }),

  // 全局搜索用户（根据邮箱）
  globalSearchZoomUsersByEmail: (email, params) => api.get('/zoom-users/search/email', { params: { email, ...params } }),

  // 全局搜索用户（根据姓名）
  globalSearchZoomUsersByName: (name, params) => api.get('/zoom-users/search/name', { params: { name, ...params } }),

  // 统计ZoomAuth下的用户数量
  countZoomUsersByAuth: (zoomAuthId) => api.get(`/zoom-users/auth/${zoomAuthId}/count`),

  // 统计各状态的用户数量
  countZoomUsersByStatus: (zoomAuthId) => api.get(`/zoom-users/auth/${zoomAuthId}/stats/status`),

  // 统计各用户类型的数量
  countZoomUsersByUserType: (zoomAuthId) => api.get(`/zoom-users/auth/${zoomAuthId}/stats/user-type`),

  // 同步Zoom用户信息
  syncZoomUserInfo: (id) => api.post(`/zoom-users/${id}/sync`),

  // 删除Zoom用户
  deleteZoomUser: (id) => api.delete(`/zoom-users/${id}`),

  // 批量删除Zoom用户
  deleteZoomUsers: (ids) => api.delete('/zoom-users/batch', { data: ids }),

  // 获取用户统计信息
  getDashboardStats: (zoomAuthId) => api.get(`/zoom-users/auth/${zoomAuthId}/dashboard`),

  // 获取全局用户统计信息
  getGlobalDashboardStats: () => api.get('/zoom-users/dashboard/global'),

  // 从Zoom API同步指定ZoomAuth的所有用户信息
  syncUsersFromZoomApi: (zoomAuthId) => api.post(`/zoom-users/auth/${zoomAuthId}/sync-from-api`),

  // 同步指定ZoomAuth的用户PMI信息
  syncUsersPmi: (zoomAuthId) => api.post(`/zoom-users/auth/${zoomAuthId}/sync-pmi`),

  // 批量同步多个ZoomAuth的子账号用户信息
  batchSyncUsersFromZoomApi: (zoomAuthIds) => api.post('/zoom-users/batch-sync-from-api', zoomAuthIds),

  // 重置用户主持人密钥
  resetHostKey: (id) => api.post(`/zoom-users/${id}/reset-host-key`),
};

// 会议API
export const meetingApi = {
  // 获取会议列表
  getMeetings: (params) => api.get('/meetings', { params }),

  // 获取最近的会议详情（用于仪表板）
  getRecentMeetingDetails: (params) => api.get('/meetings/recent-details', { params }),

  // 获取最近一周的会议详情（用于仪表板）
  getRecentWeekMeetingDetails: (params) => api.get('/meetings/recent-week-details', { params }),
  
  // 根据ID获取会议
  getMeetingById: (id) => api.get(`/meetings/${id}`),
  
  // 根据Zoom账号ID获取会议
  getMeetingsByZoomAccountId: (zoomAccountId) => api.get(`/meetings/zoom-account/${zoomAccountId}`),
  
  // 创建会议
  createMeeting: (data) => api.post('/meetings', data),
  
  // 更新会议状态
  updateMeetingStatus: (id, status) => api.put(`/meetings/${id}/status`, null, { params: { status } }),
  
  // 同步会议信息
  syncMeetingInfo: (id) => api.post(`/meetings/${id}/sync`),
  
  // 删除会议
  deleteMeeting: (id) => api.delete(`/meetings/${id}`),
  
  // 根据时间范围获取会议
  getMeetingsByTimeRange: (startDate, endDate) =>
    api.get('/meetings/time-range', { params: { startDate, endDate } }),

  // 获取Zoom会议详情（单个，兼容旧版本）
  getZoomMeetingDetail: (meetingId) => api.get(`/meetings/${meetingId}/zoom-detail`),

  // 获取Zoom会议详情列表（支持周期性会议的多个详情记录）
  getZoomMeetingDetails: (meetingId) => api.get(`/meetings/${meetingId}/zoom-details`),

  // 获取特定occurrence的会议信息
  getMeetingOccurrence: (meetingId, occurrenceId) => api.get(`/meetings/${meetingId}/occurrences/${occurrenceId}`),

  // 更新特定occurrence的会议信息
  updateMeetingOccurrence: (meetingId, occurrenceId, updateData) =>
    api.put(`/meetings/${meetingId}/occurrences/${occurrenceId}`, updateData),

  // 删除特定occurrence的会议
  deleteMeetingOccurrence: (meetingId, occurrenceId) =>
    api.delete(`/meetings/${meetingId}/occurrences/${occurrenceId}`),

  // 批量删除会议occurrences
  batchDeleteMeetingOccurrences: (meetingId, occurrenceIds) =>
    api.delete(`/meetings/${meetingId}/occurrences/batch`, { data: { occurrenceIds } }),

  // 获取会议邀请信息
  getMeetingInvitation: (meetingId) => api.get(`/meetings/${meetingId}/invitation`),
};

// 会议报告API
export const meetingReportApi = {
  // 获取会议报告列表
  getReports: (params) => api.get('/meeting-reports', { params }),

  // 根据UUID获取会议报告详情（返回最新的一条）
  getReportByUuid: (zoomMeetingUuid) => api.get(`/meeting-reports/uuid/${zoomMeetingUuid}`),

  // 根据UUID获取所有会议报告记录（支持多条记录）
  getAllReportsByUuid: (zoomMeetingUuid) => api.get(`/meeting-reports/uuid/${zoomMeetingUuid}/all`),

  // 根据会议ID获取会议报告详情（返回最新的一条）
  getReportByMeetingId: (zoomMeetingId) => api.get(`/meeting-reports/meeting-id/${zoomMeetingId}`),

  // 根据会议ID获取所有会议报告记录（支持多条记录）
  getAllReportsByMeetingId: (zoomMeetingId) => api.get(`/meeting-reports/meeting-id/${zoomMeetingId}/all`),

  // 获取指定PMI的会议报告
  getReportsByPmiRecordId: (pmiRecordId, params) => api.get(`/meeting-reports/pmi/${pmiRecordId}`, { params }),

  // 获取指定主持人的会议报告
  getReportsByHostId: (hostId, params) => api.get(`/meeting-reports/host/${hostId}`, { params }),

  // 获取最近的会议报告
  getRecentReports: (params) => api.get('/meeting-reports/recent', { params }),

  // 获取会议报告统计信息
  getReportStatistics: (params) => api.get('/meeting-reports/statistics', { params }),

  // 获取会议参会人员列表
  getParticipants: (reportId, params) => api.get(`/meeting-reports/${reportId}/participants`, { params }),

  // 获取参会人员统计信息
  getParticipantStatistics: (reportId) => api.get(`/meeting-reports/${reportId}/participants/statistics`),

  // 手动触发会议报告获取（通过Zoom会议UUID）
  triggerReportFetch: (zoomMeetingUuid) => api.post(`/meeting-reports/fetch/${zoomMeetingUuid}`),

  // 手动触发会议报告获取（通过系统会议UUID）
  triggerReportFetchByMeetingUuid: (meetingUuid) => api.post(`/meeting-reports/fetch/meeting/${meetingUuid}`),

  // 创建会议报告获取任务
  createReportTask: (data) => api.post('/meeting-reports/tasks', data),

  // 检查会议报告是否存在
  checkReportExists: (zoomMeetingUuid) => api.get(`/meeting-reports/exists/${zoomMeetingUuid}`),

  // 删除会议报告
  deleteReport: (reportId) => api.delete(`/meeting-reports/${reportId}`),

  // 批量获取会议报告
  getBatchReports: (meetingUuids) => api.post('/meeting-reports/batch', meetingUuids),

  // 导出会议报告
  exportReports: (params) => api.get('/meeting-reports/export', {
    params,
    responseType: 'blob'
  }),

  // 获取会议报告概览统计
  getReportsOverview: (params) => api.get('/meeting-reports/overview', { params }),

  // 重新获取会议报告
  refetchReport: (zoomMeetingUuid) => api.post(`/meeting-reports/refetch/${zoomMeetingUuid}`),

  // 获取会议报告获取任务状态
  getReportTasks: (params) => api.get('/meeting-reports/tasks', { params }),

  // 根据PMI Record ID手动触发会议报告获取
  triggerReportFetchByPmiRecordId: (pmiRecordId) => api.post(`/pmi-reports/pmi-record/${pmiRecordId}/fetch-reports`),

  // 根据PMI Record ID获取会议报告列表
  getMeetingReportsByPmiRecordId: (pmiRecordId) => api.get(`/pmi-reports/pmi-record/${pmiRecordId}/reports`),
};

// Webhook事件API
export const webhookApi = {
  // 获取所有Webhook事件
  getWebhookEvents: () => api.get('/webhooks/events'),

  // 根据状态获取Webhook事件
  getWebhookEventsByStatus: (status) => api.get(`/webhooks/events/status/${status}`),

  // 重放Webhook事件
  replayWebhookEvent: (eventId) => api.post(`/webhooks/events/${eventId}/replay`),
};

// PMI管理API
export const pmiApi = {
  // 生成PMI
  generatePmi: (data) => api.post('/pmi/generate', data),

  // 使用PMI开启会议室
  usePmi: (data) => api.post('/pmi/use', data),

  // 获取所有PMI记录
  getAllPmiRecords: (params) => api.get('/pmi', { params }),

  // 根据用户ID获取PMI记录
  getUserPmiRecords: (userId, params) => api.get(`/pmi/user/${userId}`, { params }),

  // 根据ID获取PMI记录
  getPmiRecordById: (id) => api.get(`/pmi/${id}`),

  // 根据PMI号码获取记录
  getPmiRecordByNumber: (pmiNumber) => api.get(`/pmi/number/${pmiNumber}`),

  // 搜索PMI记录
  searchPmiRecords: (keyword, params) => api.get('/pmi/search', {
    params: { keyword, ...params }
  }),

  // 获取PMI复制信息
  getPmiCopyText: (id) => api.get(`/pmi/${id}/copy-text`),

  // 更新PMI记录
  updatePmiRecord: (id, data) => api.put(`/pmi/${id}`, data),

  // 更新PMI状态
  updatePmiStatus: (id, status) => api.put(`/pmi/${id}/status`, null, { params: { status } }),

  // 删除PMI记录
  deletePmiRecord: (id) => api.delete(`/pmi/${id}`),

  // 获取PMI统计信息
  getPmiStats: () => api.get('/pmi/stats'),

  // 获取用户PMI统计信息
  getUserPmiStats: (userId) => api.get(`/pmi/user/${userId}/stats`),

  // 设置PMI回退状态
  setPmiFallback: (id, fallbackEnabled) => api.put(`/admin/pmi/${id}/fallback`, { fallbackEnabled }),

  // 批量设置PMI回退状态
  batchSetPmiFallback: (pmiIds, fallbackEnabled) => api.put('/admin/pmi/batch-fallback', { pmiIds, fallbackEnabled }),
};

// PMI计划管理API
export const pmiScheduleApi = {
  // 创建PMI计划
  createSchedule: (data) => api.post('/pmi-schedules', data),

  // 创建PMI计划（合并冲突窗口）
  createScheduleWithMerge: (data) => api.post('/pmi-schedules/create-with-merge', data),

  // 更新PMI计划
  updateSchedule: (id, data) => api.put(`/pmi-schedules/${id}`, data),

  // 删除PMI计划
  deleteSchedule: (id) => api.delete(`/pmi-schedules/${id}`),

  // 获取PMI计划详情
  getSchedule: (id) => api.get(`/pmi-schedules/${id}`),

  // 获取计划列表
  getSchedules: (params) => api.get('/pmi-schedules', { params }),

  // 搜索计划
  searchSchedules: (pmiRecordId, keyword, params) => api.get('/pmi-schedules', {
    params: { pmiRecordId, keyword, ...params }
  }),

  // 更新计划状态
  updateScheduleStatus: (id, status) => api.put(`/pmi-schedules/${id}/status`, null, { params: { status } }),

  // 获取计划的窗口列表
  getScheduleWindows: (id, params) => api.get(`/pmi-schedules/${id}/windows`, { params }),

  // 更新计划窗口
  updateScheduleWindow: (windowId, data) => api.put(`/pmi-schedule-windows/${windowId}`, data),

  // 删除计划窗口
  deleteScheduleWindow: (windowId) => api.delete(`/pmi-schedule-windows/${windowId}`),

  // 关闭计划窗口
  closeScheduleWindow: (windowId) => api.put(`/pmi-schedule-windows/${windowId}/close`),

  // 延长计划窗口
  extendScheduleWindow: (windowId, minutes) => api.put(`/pmi-schedule-windows/${windowId}/extend?minutes=${minutes}`),



  // 批量删除计划
  batchDeleteSchedules: (scheduleIds) => api.delete('/pmi-schedules/batch', { data: scheduleIds }),

  // 批量更新计划状态
  batchUpdateStatus: (data) => api.put('/pmi-schedules/batch/status', data),
};

// Zoom用户PMI管理API
export const zoomUserPmiApi = {
  // 生成随机PMI号码
  generatePmi: () => api.get('/admin/zoom-users/generate-pmi'),

  // 验证PMI号码格式
  validatePmi: (pmi) => api.post('/admin/zoom-users/validate-pmi', { pmi }),

  // 更新用户原始PMI
  updateOriginalPmi: (userId, pmi) => api.put(`/admin/zoom-users/${userId}/original-pmi`, { pmi }),

  // 回收用户账号
  recycleAccount: (userId) => api.post(`/admin/zoom-users/${userId}/recycle`),

  // 获取用户PMI信息
  getUserPmiInfo: (userId) => api.get(`/admin/zoom-users/${userId}/pmi-info`),

  // 测试优化后的PMI设置逻辑
  testOptimizedPmiSetup: (userId, data) => api.post(`/admin/zoom-users/${userId}/test-optimized-pmi`, data),
};

// 公共PMI使用API
export const publicPmiApi = {
  // 获取PMI基本信息
  getPmiInfo: (pmiNumber) => api.get(`/public/pmi/${pmiNumber}`),

  // 一键开启PMI
  activatePmi: (pmiNumber) => api.post(`/public/pmi/${pmiNumber}/activate`),

  // 获取PMI复制信息
  getPmiCopyText: (pmiNumber) => api.get(`/public/pmi/${pmiNumber}/copy-text`),
};

// 系统配置API
export const systemConfigApi = {
  // 获取配置列表
  getConfigs: (params) => api.get('/admin/system/config', { params }),

  // 根据ID获取配置
  getConfigById: (id) => api.get(`/admin/system/config/${id}`),

  // 根据配置键获取配置值
  getConfigValues: (keys) => api.get('/admin/system/config/value', { params: { keys } }),

  // 根据前缀获取配置
  getConfigsByPrefix: (prefix) => api.get(`/admin/system/config/prefix/${prefix}`),

  // 创建配置
  createConfig: (data) => api.post('/admin/system/config', data),

  // 更新配置
  updateConfig: (id, data) => api.put(`/admin/system/config/${id}`, data),

  // 批量更新配置
  batchUpdateConfigs: (configs) => api.put('/admin/system/config/batch', configs),

  // 删除配置
  deleteConfig: (id) => api.delete(`/admin/system/config/${id}`),

  // 启用/禁用配置
  toggleStatus: (id) => api.put(`/admin/system/config/${id}/toggle`),

  // 获取配置统计信息
  getStatistics: () => api.get('/admin/system/config/statistics'),
};

// Join Account Rental令牌API
export const joinAccountTokenApi = {
  // 批量生成权益链接
  batchGenerate: (params) => api.post('/admin/join-account/tokens/batch-generate', null, { params }),

  // 获取令牌列表
  getTokens: (params) => api.get('/admin/join-account/tokens', { params }),

  // 根据ID获取令牌详情
  getTokenById: (id) => api.get(`/admin/join-account/tokens/${id}`),

  // 根据Token编号获取令牌详情
  getTokenByNumber: (tokenNumber) => api.get(`/admin/join-account/tokens/by-number/${tokenNumber}`),

  // 获取令牌统计信息
  getStatistics: () => api.get('/admin/join-account/tokens/statistics'),

  // 获取可导出的令牌
  getExportableTokens: () => api.get('/admin/join-account/tokens/exportable'),

  // 获取可预约的令牌
  getReservableTokens: () => api.get('/admin/join-account/tokens/reservable'),

  // 获取可作废的令牌
  getCancellableTokens: () => api.get('/admin/join-account/tokens/cancellable'),

  // 检查Token是否可以预约
  canReserveToken: (tokenNumber) => api.get(`/admin/join-account/tokens/${tokenNumber}/can-reserve`),

  // 生成权益链接
  generateTokenLink: (tokenNumber) => api.get(`/admin/join-account/tokens/${tokenNumber}/link`),

  // 批量标记为已导出
  markAsExported: (tokenIds) => api.put('/admin/join-account/tokens/mark-exported', tokenIds),

  // 批量导出权益链接为Excel文件
  exportToExcel: (tokenIds, operator) => api.post('/admin/join-account/tokens/export', tokenIds, {
    params: { operator },
    responseType: 'blob'
  }),

  // 批量作废令牌
  cancelTokens: (tokenIds, params) => api.put('/admin/join-account/tokens/cancel', tokenIds, { params }),

  // 预约令牌
  reserveToken: (tokenNumber, params) => api.put(`/admin/join-account/tokens/${tokenNumber}/reserve`, null, { params }),

  // 激活令牌
  activateToken: (tokenNumber, params) => api.put(`/admin/join-account/tokens/${tokenNumber}/activate`, null, { params }),

  // 完成令牌
  completeToken: (tokenNumber) => api.put(`/admin/join-account/tokens/${tokenNumber}/complete`),
};

// Join Account使用窗口API
export const joinAccountWindowApi = {
  // 获取使用窗口列表
  getWindows: (params) => api.get('/admin/join-account/windows', { params }),

  // 根据ID获取使用窗口详情
  getWindowById: (id) => api.get(`/admin/join-account/windows/${id}`),

  // 根据Token编号获取使用窗口
  getWindowByToken: (tokenNumber) => api.get(`/admin/join-account/windows/by-token/${tokenNumber}`),

  // 根据Zoom账号ID获取使用窗口
  getWindowsByZoomUserId: (zoomUserId) => api.get(`/admin/join-account/windows/by-zoom-user/${zoomUserId}`),

  // 创建使用窗口
  createWindow: (params) => api.post('/admin/join-account/windows', null, { params }),

  // 开启使用窗口
  openWindow: (id) => api.put(`/admin/join-account/windows/${id}/open`),

  // 关闭使用窗口
  closeWindow: (id) => api.put(`/admin/join-account/windows/${id}/close`),

  // 删除使用窗口
  deleteWindow: (id) => api.delete(`/admin/join-account/windows/${id}`),

  // 获取需要开启的窗口
  getWindowsToOpen: () => api.get('/admin/join-account/windows/to-open'),

  // 获取需要关闭的窗口
  getWindowsToClose: () => api.get('/admin/join-account/windows/to-close'),

  // 获取使用窗口统计信息
  getStatistics: () => api.get('/admin/join-account/windows/statistics'),

  // 清除窗口错误信息
  clearWindowError: (id) => api.put(`/admin/join-account/windows/${id}/clear-error`),

  // 获取即将开启的窗口
  getUpcomingWindows: (minutes = 60) => api.get('/admin/join-account/windows/upcoming', { params: { minutes } }),

  // 获取即将关闭的窗口
  getExpiringWindows: (minutes = 60) => api.get('/admin/join-account/windows/expiring', { params: { minutes } }),
};

// Join Account密码管理API
export const joinAccountPasswordApi = {
  // 手动变更密码
  changePassword: (zoomUserId, params) => api.post(`/admin/join-account/passwords/change/${zoomUserId}`, null, { params }),

  // 批量重置密码
  batchResetPasswords: (zoomUserIds, params) => api.post('/admin/join-account/passwords/batch-reset', zoomUserIds, { params }),

  // 生成随机密码
  generatePassword: (length = 12) => api.get('/admin/join-account/passwords/generate', { params: { length } }),

  // 验证密码强度
  validatePassword: (password) => api.post('/admin/join-account/passwords/validate', null, { params: { password } }),

  // 获取密码变更日志
  getPasswordLogs: (params) => api.get('/admin/join-account/passwords/logs', { params }),

  // 根据Zoom账号ID获取密码变更日志
  getPasswordLogsByZoomUserId: (zoomUserId, params) => api.get(`/admin/join-account/passwords/logs/by-zoom-user/${zoomUserId}`, { params }),

  // 根据窗口ID获取密码变更日志
  getPasswordLogsByWindowId: (windowId) => api.get(`/admin/join-account/passwords/logs/by-window/${windowId}`),

  // 获取密码变更统计信息
  getStatistics: () => api.get('/admin/join-account/passwords/statistics'),

  // 获取系统自动变更的日志
  getSystemChangeLogs: () => api.get('/admin/join-account/passwords/logs/system'),

  // 获取手动变更的日志
  getManualChangeLogs: () => api.get('/admin/join-account/passwords/logs/manual'),

  // 获取频繁变更密码的账号
  getFrequentlyChangedAccounts: (params) => api.get('/admin/join-account/passwords/frequent-changes', { params }),
};

// Join Account智能分配API
export const joinAccountAllocationApi = {
  // 智能分配账号
  allocateAccount: (params) => api.post('/admin/join-account/allocation/allocate', null, { params }),

  // 获取分配统计信息
  getStatistics: () => api.get('/admin/join-account/allocation/statistics'),

  // 测试分配算法
  testAllocation: (params) => api.post('/admin/join-account/allocation/test', null, { params }),
};

export default api;
