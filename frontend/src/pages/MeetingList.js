import React, { useState, useEffect, useCallback } from 'react';
import { Table, Button, Modal, Form, Input, Select, DatePicker, Space, Tag, message, Popconfirm, Row, Col, Switch, Divider, Descriptions, Spin, Typography, Tooltip, Checkbox, Radio, Card } from 'antd';
import { PlusOutlined, SyncOutlined, DeleteOutlined, LinkOutlined, DownOutlined, RightOutlined, InfoCircleOutlined, CalendarOutlined, CopyOutlined, EditOutlined, SettingOutlined, UserOutlined, EyeOutlined } from '@ant-design/icons';
import { meetingApi, zoomUserApi } from '../services/api';
import { useDomainConfig } from '../hooks/useSystemConfig';
import EditOccurrenceModal from '../components/EditOccurrenceModal';
import BatchOccurrenceModal from '../components/BatchOccurrenceModal';
import MeetingReportModal from '../components/MeetingReportModal';
import { copyMeetingInvitation } from '../utils/copyMeetingInfo';
import { useParams } from 'react-router-dom';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const MeetingList = () => {
  const { meetingUuid } = useParams(); // 获取URL参数
  const [meetings, setMeetings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [submitting, setSubmitting] = useState(false);

  // 会议报告弹窗状态
  const [reportModalVisible, setReportModalVisible] = useState(false);
  const [currentMeetingForReport, setCurrentMeetingForReport] = useState(null);

  // 使用域名配置Hook
  const { generateUserFrontendLink, generateMeetingHostLink } = useDomainConfig();

  // Zoom用户搜索相关状态
  const [zoomUsers, setZoomUsers] = useState([]);
  const [zoomUserSearchLoading, setZoomUserSearchLoading] = useState(false);
  const [zoomUserSearchTimeout, setZoomUserSearchTimeout] = useState(null);

  // 展开行相关状态
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [zoomMeetingDetails, setZoomMeetingDetails] = useState({});
  const [loadingDetails, setLoadingDetails] = useState({});

  // 编辑和批量操作相关状态
  const [editOccurrenceVisible, setEditOccurrenceVisible] = useState(false);
  const [batchOccurrenceVisible, setBatchOccurrenceVisible] = useState(false);
  const [currentEditOccurrence, setCurrentEditOccurrence] = useState(null);
  const [currentEditMeetingId, setCurrentEditMeetingId] = useState(null);
  const [currentMainDetail, setCurrentMainDetail] = useState(null);
  const [currentOccurrences, setCurrentOccurrences] = useState([]);

  // 周期性会议相关状态
  const [isRecurring, setIsRecurring] = useState(false);
  const [recurrenceType, setRecurrenceType] = useState('DAILY');
  const [monthlyType, setMonthlyType] = useState('DAY_OF_MONTH');

  // 密码快速选择状态
  const [passwordValue, setPasswordValue] = useState('');

  // 周期描述状态
  const [recurrenceDescription, setRecurrenceDescription] = useState('');

  useEffect(() => {
    loadMeetings();
    loadZoomUsers();
  }, []);

  // 处理URL参数，如果有meetingUuid则自动打开对应的会议报告
  useEffect(() => {
    if (meetingUuid && meetings.length > 0 && !reportModalVisible) {
      const meeting = meetings.find(m => m.meetingUuid === meetingUuid);
      if (meeting) {
        handleViewMeetingReport(meeting);
      }
    }
  }, [meetingUuid, meetings.length]); // 只依赖meetings的长度，而不是整个数组

  // 获取Zoom会议详情（支持周期性会议的多个实例）
  const loadZoomMeetingDetail = async (meetingId, forceReload = false) => {
    if (!forceReload && (zoomMeetingDetails[meetingId] !== undefined || loadingDetails[meetingId])) {
      return; // 已经加载过或正在加载中
    }

    setLoadingDetails(prev => ({ ...prev, [meetingId]: true }));

    try {
      // 使用新的API获取所有会议详情记录
      const response = await meetingApi.getZoomMeetingDetails(meetingId);
      // 后端返回ZoomMeetingDetail数组
      if (response.data && Array.isArray(response.data)) {
        setZoomMeetingDetails(prev => ({
          ...prev,
          [meetingId]: response.data
        }));
        console.log(`加载会议 ${meetingId} 的详情成功，共 ${response.data.length} 条记录`);
      } else {
        console.error('No zoom meeting detail data received');
        setZoomMeetingDetails(prev => ({
          ...prev,
          [meetingId]: []
        }));
      }
    } catch (error) {
      console.error('Error loading zoom meeting detail:', error);
      // 如果是404错误，说明没有找到详情数据
      if (error.response && error.response.status === 404) {
        setZoomMeetingDetails(prev => ({
          ...prev,
          [meetingId]: []
        }));
      }
    } finally {
      setLoadingDetails(prev => ({ ...prev, [meetingId]: false }));
    }
  };

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 生成周期描述
  const generateRecurrenceDescription = (formValues) => {
    if (!formValues.isRecurring) {
      return '';
    }

    let description = '';

    // 重复类型和间隔
    const interval = formValues.repeatInterval || 1;

    if (formValues.recurrenceType === 'DAILY') {
      if (interval > 1) {
        description = `每${interval}天`;
      } else {
        description = '每天';
      }
    } else if (formValues.recurrenceType === 'WEEKLY') {
      if (interval > 1) {
        description = `每${interval}周`;
      } else {
        description = '每周';
      }

      // 添加星期几的描述
      if (formValues.weeklyDays && formValues.weeklyDays.length > 0) {
        const dayNames = {
          0: '日', 1: '一', 2: '二', 3: '三', 4: '四', 5: '五', 6: '六'
        };
        const selectedDays = formValues.weeklyDays
          .sort((a, b) => a - b)
          .map(day => `星期${dayNames[day]}`)
          .join('、');
        description += `，在${selectedDays}`;
      }
    } else if (formValues.recurrenceType === 'MONTHLY') {
      if (interval > 1) {
        description = `每${interval}个月`;
      } else {
        description = '每月';
      }

      // 添加每月重复的详细描述
      if (formValues.monthlyType === 'DAY_OF_MONTH' && formValues.monthlyDay) {
        description += `，第${formValues.monthlyDay}天`;
      } else if (formValues.monthlyType === 'DAY_OF_WEEK' && formValues.monthlyDay && formValues.monthlyWeekDay !== undefined) {
        const weekOrder = {
          1: '第一', 2: '第二', 3: '第三', 4: '第四', 5: '最后一'
        };
        const weekDayNames = {
          0: '星期天', 1: '星期一', 2: '星期二', 3: '星期三',
          4: '星期四', 5: '星期五', 6: '星期六'
        };
        description += `，${weekOrder[formValues.monthlyDay]}${weekDayNames[formValues.monthlyWeekDay]}`;
      }
    }

    // 结束条件
    if (formValues.endType === 'NO_END') {
      description += '，无结束时间';
    } else if (formValues.endType === 'BY_DATE' && formValues.endDateTime) {
      description += `，直至${formValues.endDateTime.format('YYYY年MM月DD日')}`;
    } else if (formValues.endType === 'BY_TIMES' && formValues.endTimes) {
      description += `，${formValues.endTimes}个会议`;
    }

    return description;
  };

  // 监听表单值变化，动态更新周期描述
  const updateRecurrenceDescription = () => {
    const formValues = form.getFieldsValue();
    const description = generateRecurrenceDescription(formValues);
    setRecurrenceDescription(description);
  };

  const loadMeetings = async () => {
    try {
      setLoading(true);
      const response = await meetingApi.getMeetings({ page: 0, size: 100 });
      console.log('API响应:', response);
      console.log('API响应数据:', response.data);
      setMeetings(response.data.content || []);
    } catch (error) {
      console.error('加载会议列表失败:', error);
    } finally {
      setLoading(false);
    }
  };



  // 加载符合条件的Zoom用户（LICENSED + PUBLIC_HOST）
  const loadZoomUsers = async () => {
    try {
      const response = await zoomUserApi.getZoomUsersByUserTypeAndAccountUsage('LICENSED', 'PUBLIC_HOST');
      setZoomUsers(response.data || []);
    } catch (error) {
      console.error('加载Zoom用户列表失败:', error);
    }
  };





  // 搜索Zoom用户
  const handleZoomUserSearch = (searchText) => {
    // 清除之前的搜索定时器
    if (zoomUserSearchTimeout) {
      clearTimeout(zoomUserSearchTimeout);
    }

    if (!searchText || searchText.trim() === '') {
      // 清空搜索时恢复原始列表
      loadZoomUsers();
      setZoomUserSearchLoading(false);
      return;
    }

    if (searchText.trim().length < 2) {
      setZoomUserSearchLoading(false);
      return;
    }

    setZoomUserSearchLoading(true);

    // 设置新的搜索定时器
    const newTimeout = setTimeout(async () => {
      try {
        const response = await zoomUserApi.searchZoomUsersByUserTypeAndAccountUsage('LICENSED', 'PUBLIC_HOST', {
          keyword: searchText.trim(),
          page: 0,
          size: 50
        });
        setZoomUsers(response.data.content || []);
      } catch (error) {
        console.error('搜索Zoom用户失败:', error);
        // 搜索失败时恢复原始列表
        loadZoomUsers();
      } finally {
        setZoomUserSearchLoading(false);
      }
    }, 500);

    setZoomUserSearchTimeout(newTimeout);
  };



  const handleCreate = () => {
    setModalVisible(true);
    setSubmitting(false); // 重置提交状态
    form.resetFields();
    setIsRecurring(false); // 重置周期性会议状态
    setRecurrenceType('DAILY'); // 重置重复类型
    setMonthlyType('DAY_OF_MONTH'); // 重置月度重复类型
    setPasswordValue(''); // 重置密码状态
    setRecurrenceDescription(''); // 重置周期描述
    // 设置默认开始时间为当前时间的下一个小时
    const defaultStartTime = dayjs().add(1, 'hour').startOf('hour');
    form.setFieldsValue({
      startTime: defaultStartTime,
      durationHours: 1,
      durationMinutes: 0,
      timezone: 'Asia/Shanghai',
      type: 'SCHEDULED',
      // 会议设置默认值
      joinBeforeHost: true,
      muteUponEntry: false,
      waitingRoom: false,
      languageInterpretation: false,
      // 周期性会议默认值
      isRecurring: false,
      recurrenceType: 'DAILY',
      repeatInterval: 1,
      endType: 'NO_END',
    });
  };

  const handleDelete = async (id) => {
    try {
      const response = await meetingApi.deleteMeeting(id);

      // 检查响应中是否有警告信息
      if (response.data.warning) {
        message.warning(response.data.message);
      } else {
        message.success('删除成功');
      }

      loadMeetings();
    } catch (error) {
      console.error('删除会议失败:', error);
      message.error('删除会议失败');
    }
  };

  // 复制邀请链接（统一使用新的复制方法）
  const handleCopyInvitation = async (occurrence, mainDetail) => {
    await copyMeetingInvitation(mainDetail, occurrence);
  };

  // 复制Zoom邀请格式的会议信息（统一使用新的复制方法）
  const handleCopyZoomInvitation = async (meeting) => {
    await copyMeetingInvitation(meeting);
  };

  // 复制主持信息
  const handleCopyHostInfo = async (meeting) => {
    try {
      // 获取会议的Zoom详情，包含主持人密钥和密码
      const detailsResponse = await meetingApi.getZoomMeetingDetails(meeting.id);
      const meetingDetails = detailsResponse.data || [];

      const hostUrl = generateMeetingHostLink(meeting.meetingUuid);

      let hostInfo = `会议主持信息

会议主题：${meeting.topic}
会议号：${meeting.zoomMeetingId}`;

      // 如果有会议详情，添加会议密码
      if (meetingDetails.length > 0) {
        const firstDetail = meetingDetails[0];
        if (firstDetail.password) {
          hostInfo += `
会议密码：${firstDetail.password}`;
        }
      }

      // 添加主持人密钥（在主持人页面链接上面）
      if (meetingDetails.length > 0) {
        const firstDetail = meetingDetails[0];
        if (firstDetail.hostKey) {
          hostInfo += `
主持人密钥：${firstDetail.hostKey}`;
        }
      }

      hostInfo += `
主持人页面：${hostUrl}

注意：请勿分享给参会者`;

      await navigator.clipboard.writeText(hostInfo);
      message.success('主持信息已复制到剪贴板');
    } catch (error) {
      console.error('复制主持信息失败:', error);

      // 降级方案：使用简单的主持信息
      try {
        const hostUrl = generateMeetingHostLink(meeting.meetingUuid);
        const fallbackInfo = `会议主持： ${hostUrl}
注意：请勿分享给参会者`;

        await navigator.clipboard.writeText(fallbackInfo);
        message.success('主持信息已复制到剪贴板');
      } catch (fallbackError) {
        message.error('复制失败，请手动复制');
      }
    }
  };

  // 编辑occurrence
  const handleEditOccurrence = (meetingId, occurrence, mainDetail) => {
    setCurrentEditMeetingId(meetingId);
    setCurrentEditOccurrence(occurrence);
    setCurrentMainDetail(mainDetail);
    setEditOccurrenceVisible(true);
  };

  // 批量管理occurrence
  const handleBatchManageOccurrences = (meetingId, occurrences, mainDetail) => {
    setCurrentEditMeetingId(meetingId);
    setCurrentOccurrences(occurrences);
    setCurrentMainDetail(mainDetail);
    setBatchOccurrenceVisible(true);
  };

  // 编辑成功回调
  const handleEditSuccess = useCallback(() => {
    // 重新加载会议详情
    if (currentEditMeetingId) {
      // 完全清除缓存
      setZoomMeetingDetails(prev => {
        const newState = { ...prev };
        delete newState[currentEditMeetingId];
        return newState;
      });
      setLoadingDetails(prev => {
        const newState = { ...prev };
        delete newState[currentEditMeetingId];
        return newState;
      });

      // 清除当前编辑的相关状态
      setCurrentOccurrences([]);
      setCurrentEditOccurrence(null);
      setCurrentMainDetail(null);

      // 立即重新加载
      loadZoomMeetingDetail(currentEditMeetingId, true);
    }
  }, [currentEditMeetingId]);

  // 删除occurrence
  const handleDeleteOccurrence = async (meetingId, occurrence) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除这场会议实例吗？删除后无法恢复。`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await meetingApi.deleteMeetingOccurrence(meetingId, occurrence.occurrenceId);

          // 检查响应中是否有警告信息
          if (response.data.warning) {
            message.warning(response.data.message);
          } else {
            message.success('会议实例删除成功');
          }

          // 完全清除缓存并重新加载会议详情
          setZoomMeetingDetails(prev => {
            const newState = { ...prev };
            delete newState[meetingId];
            return newState;
          });
          setLoadingDetails(prev => {
            const newState = { ...prev };
            delete newState[meetingId];
            return newState;
          });
          // 强制重新加载会议详情
          loadZoomMeetingDetail(meetingId, true);
        } catch (error) {
          console.error('Error deleting occurrence:', error);
          message.error('删除会议实例失败');
        }
      },
    });
  };

  const handleSync = async (id) => {
    try {
      await meetingApi.syncMeetingInfo(id);
      message.success('同步成功');
      loadMeetings();
    } catch (error) {
      console.error('同步会议信息失败:', error);
    }
  };

  const handleStatusChange = async (id, status) => {
    try {
      await meetingApi.updateMeetingStatus(id, status);
      message.success('状态更新成功');
      loadMeetings();
    } catch (error) {
      console.error('更新状态失败:', error);
    }
  };

  // 查看会议报告
  const handleViewMeetingReport = (meeting) => {
    const meetingForReport = {
      meetingUuid: meeting.meetingUuid,        // 系统内部UUID
      zoomMeetingUuid: meeting.meetingUuid,    // 保持向后兼容
      zoomMeetingId: meeting.zoomMeetingId,
      topic: meeting.topic
    };
    setCurrentMeetingForReport(meetingForReport);
    setReportModalVisible(true);
  };

  const handleSubmit = async (values) => {
    try {
      setSubmitting(true); // 开始提交，设置loading状态
      console.log('=== 表单提交开始 ===');
      console.log('原始表单values:', JSON.stringify(values, null, 2));

      const totalMinutes = (values.durationHours || 0) * 60 + (values.durationMinutes || 0);
      console.log('时长计算:', {
        durationHours: values.durationHours,
        durationMinutes: values.durationMinutes,
        totalMinutes: totalMinutes
      });

      const submitData = {
        ...values,
        startTime: values.startTime.format('YYYY-MM-DDTHH:mm:ss'),
        durationMinutes: totalMinutes,
      };

      console.log('处理后的submitData:', JSON.stringify(submitData, null, 2));

      // 处理Zoom用户选择：如果选择了zoomUserId，将其映射为userId字段
      if (submitData.zoomUserId) {
        const selectedUser = zoomUsers.find(user => user.id === submitData.zoomUserId);
        if (selectedUser && selectedUser.user && selectedUser.user.id) {
          // 使用ZoomUser关联的User的ID
          submitData.userId = selectedUser.user.id;
        } else {
          // 未关联系统用户的ZoomUser也可以用于创建会议
          // 直接使用ZoomUser的ID作为标识
          submitData.userId = null; // 或者可以使用其他标识方式
          submitData.zoomUserIdForMeeting = submitData.zoomUserId; // 保留ZoomUser ID用于会议创建
        }
      }

      // 确保选择了Zoom账号
      if (!submitData.zoomUserId) {
        message.error('请选择Zoom账号');
        return;
      }

      // 使用状态中的周期性会议设置，而不是表单值
      submitData.isRecurring = isRecurring;

      // 处理周期性会议数据
      if (isRecurring) {
        // 如果选择了周期性会议，确保相关字段有值
        if (!submitData.recurrenceType) {
          message.error('请选择重复类型');
          return;
        }
        if (submitData.recurrenceType === 'WEEKLY' && (!submitData.weeklyDays || submitData.weeklyDays.length === 0)) {
          message.error('请选择至少一个星期');
          return;
        }
        if (submitData.recurrenceType === 'MONTHLY') {
          if (!submitData.monthlyType) {
            message.error('请选择月度重复方式');
            return;
          }
          if (!submitData.monthlyDay) {
            message.error('请选择日期或第几个');
            return;
          }
          if (submitData.monthlyType === 'DAY_OF_WEEK' && submitData.monthlyWeekDay === undefined) {
            message.error('请选择星期几');
            return;
          }
        }
        if (submitData.endType === 'BY_DATE' && !submitData.endDateTime) {
          message.error('请选择结束日期');
          return;
        }
        if (submitData.endType === 'BY_TIMES' && !submitData.endTimes) {
          message.error('请输入结束次数');
          return;
        }
        // 处理结束日期时间格式
        if (submitData.endDateTime) {
          submitData.endDateTime = submitData.endDateTime.format('YYYY-MM-DDTHH:mm:ss');
        }
        // 处理weeklyDays数组转换为字符串
        if (submitData.weeklyDays && Array.isArray(submitData.weeklyDays)) {
          submitData.weeklyDays = submitData.weeklyDays.join(',');
        }
      } else {
        // 如果不是周期性会议，清除相关字段
        delete submitData.recurrenceType;
        delete submitData.repeatInterval;
        delete submitData.weeklyDays;
        delete submitData.monthlyType;
        delete submitData.monthlyDay;
        delete submitData.monthlyWeekDay;
        delete submitData.endType;
        delete submitData.endDateTime;
        delete submitData.endTimes;
      }

      // 移除单独的小时和分钟字段以及设置字段（暂时不发送到后端）
      delete submitData.durationHours;
      delete submitData.joinBeforeHost;
      delete submitData.muteUponEntry;
      delete submitData.waitingRoom;
      delete submitData.languageInterpretation;
      // 保留zoomUserId用于后端处理
      // delete submitData.zoomUserId; // 不要删除这个字段，后端需要它

      console.log('原始表单数据:', values);
      console.log('提交的会议数据:', submitData);
      console.log('会议时长计算:', {
        durationHours: values.durationHours,
        durationMinutes: values.durationMinutes,
        totalMinutes: totalMinutes,
        finalDurationMinutes: submitData.durationMinutes
      });
      console.log('周期性会议状态:', {
        isRecurring: submitData.isRecurring,
        recurrenceType: submitData.recurrenceType,
        repeatInterval: submitData.repeatInterval,
        endType: submitData.endType,
        endDateTime: submitData.endDateTime,
        endTimes: submitData.endTimes
      });
      console.log('会议设置:', {
        joinBeforeHost: values.joinBeforeHost,
        muteUponEntry: values.muteUponEntry,
        waitingRoom: values.waitingRoom,
        languageInterpretation: values.languageInterpretation
      });

      await meetingApi.createMeeting(submitData);
      message.success('安排成功');
      setModalVisible(false);
      loadMeetings();
    } catch (error) {
      console.error('安排会议失败:', error);
      message.error('安排会议失败，请重试');
    } finally {
      setSubmitting(false); // 无论成功失败都要重置loading状态
    }
  };

  const openJoinUrl = (url) => {
    if (url) {
      window.open(url, '_blank');
    }
  };

  // 渲染展开行内容
  const renderExpandedRow = (record) => {
    const meetingId = record.id;
    const details = zoomMeetingDetails[meetingId];
    const isLoading = loadingDetails[meetingId];

    // 如果正在加载或者数据还未加载，显示加载状态
    if (isLoading || details === undefined) {
      return (
        <div style={{ padding: '16px', textAlign: 'center' }}>
          <Spin size="small" />
          <span style={{ marginLeft: 8 }}>加载Zoom会议详情中...</span>
        </div>
      );
    }

    // 只有当数据已加载且为空数组时，才显示"暂无信息"
    if (details && details.length === 0) {
      return (
        <div style={{ padding: '16px', color: '#999' }}>
          <InfoCircleOutlined style={{ marginRight: 8 }} />
          暂无Zoom会议详情信息
        </div>
      );
    }

    // 找到主记录和occurrence记录
    const mainDetail = details.find(d => d.isMainOccurrence) || details[0];
    const occurrences = details.filter(d => !d.isMainOccurrence);

    // 检查是否是周期性会议但所有occurrence都被删除了
    const isRecurringWithNoOccurrences = mainDetail && mainDetail.isMainOccurrence && occurrences.length === 0 && mainDetail.recurrence;

    return (
      <div style={{ padding: '16px', backgroundColor: '#fafafa' }}>
        {/* 如果是周期性会议但所有实例都被删除了，显示特殊提示 */}
        {isRecurringWithNoOccurrences && (
          <div style={{ padding: '16px', textAlign: 'center', backgroundColor: '#fff7e6', border: '1px solid #ffd591', borderRadius: '6px', marginBottom: '16px' }}>
            <InfoCircleOutlined style={{ marginRight: 8, color: '#fa8c16' }} />
            <span style={{ color: '#fa8c16' }}>这是一个周期性会议，但所有会议实例都已被删除</span>
          </div>
        )}

        {/* 主会议信息 */}
        <Descriptions
          title="Zoom会议详情"
          size="small"
          column={isMobileView ? 1 : 2}
          bordered
        >
          <Descriptions.Item label="Zoom会议ID">
            <Text copyable>{mainDetail.zoomMeetingId}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="UUID">
            <Text copyable>{mainDetail.uuid || '无'}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="会议状态">
            <Tag color={mainDetail.status === 'waiting' ? 'blue' : 'green'}>
              {mainDetail.status || '未知'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="会议类型">
            {mainDetail.type === 1 ? '即时会议' :
             mainDetail.type === 2 ? '预定会议' :
             mainDetail.type === 3 ? '周期性会议(无固定时间)' :
             mainDetail.type === 8 ? '周期性会议(固定时间)' :
             `类型${mainDetail.type}`}
          </Descriptions.Item>
          <Descriptions.Item label="主持人ID">
            {mainDetail.hostId || '无'}
          </Descriptions.Item>
          <Descriptions.Item label="主持人邮箱">
            {mainDetail.hostEmail || '无'}
          </Descriptions.Item>
          <Descriptions.Item label="时区">
            {mainDetail.timezone || '无'}
          </Descriptions.Item>
          <Descriptions.Item label="是否加密">
            <Tag color={mainDetail.encrypted ? 'green' : 'default'}>
              {mainDetail.encrypted ? '是' : '否'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="等候室">
            <Tag color={mainDetail.waitingRoom ? 'blue' : 'default'}>
              {mainDetail.waitingRoom ? '启用' : '禁用'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="入会时静音">
            <Tag color={mainDetail.muteUponEntry ? 'orange' : 'default'}>
              {mainDetail.muteUponEntry ? '是' : '否'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="允许主持人前加入">
            <Tag color={mainDetail.joinBeforeHost ? 'green' : 'default'}>
              {mainDetail.joinBeforeHost ? '是' : '否'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="自动录制">
            {mainDetail.autoRecording || '无'}
          </Descriptions.Item>
          {mainDetail.joinUrl && (
            <Descriptions.Item label="加入链接" span={isMobileView ? 1 : 2}>
              <Text copyable ellipsis style={{ maxWidth: '300px' }}>
                {mainDetail.joinUrl}
              </Text>
            </Descriptions.Item>
          )}
          {mainDetail.startUrl && (
            <Descriptions.Item label="主持人链接" span={isMobileView ? 1 : 2}>
              <Text copyable ellipsis style={{ maxWidth: '300px' }}>
                {mainDetail.startUrl}
              </Text>
            </Descriptions.Item>
          )}
          {mainDetail.password && (
            <Descriptions.Item label="会议密码">
              <Text copyable>{mainDetail.password}</Text>
            </Descriptions.Item>
          )}
          <Descriptions.Item label="Zoom创建时间" span={isMobileView ? 1 : 2}>
            {mainDetail.zoomCreatedAt ? dayjs(mainDetail.zoomCreatedAt).format('YYYY-MM-DD HH:mm:ss') : '无'}
          </Descriptions.Item>
        </Descriptions>

        {/* 周期性会议信息 */}
        {record.isRecurring && (
          <div style={{ marginTop: '16px' }}>
            <Descriptions
              title="周期性会议设置"
              size="small"
              column={isMobileView ? 1 : 2}
              bordered
            >
              <Descriptions.Item label="重复类型">
                <Tag color="blue">
                  {record.recurrenceType === 'DAILY' ? '每天' :
                   record.recurrenceType === 'WEEKLY' ? '每周' :
                   record.recurrenceType === 'MONTHLY' ? '每月' :
                   record.recurrenceType}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="重复间隔">
                {(() => {
                  const interval = record.repeatInterval || 1;
                  let unit = '';
                  if (record.recurrenceType === 'DAILY') {
                    unit = '天';
                  } else if (record.recurrenceType === 'WEEKLY') {
                    unit = '周';
                  } else if (record.recurrenceType === 'MONTHLY') {
                    unit = '个月';
                  }
                  return `每 ${interval} ${unit}`;
                })()}
              </Descriptions.Item>
              {/* 显示星期几信息（仅当重复类型为每周时） */}
              {record.recurrenceType === 'WEEKLY' && record.weeklyDays && (
                <Descriptions.Item label="发生于" span={isMobileView ? 1 : 2}>
                  {(() => {
                    const dayNames = {
                      0: '星期日', 1: '星期一', 2: '星期二', 3: '星期三',
                      4: '星期四', 5: '星期五', 6: '星期六'
                    };
                    const selectedDays = record.weeklyDays
                      .split(',')
                      .map(day => parseInt(day.trim()))
                      .filter(day => !isNaN(day))
                      .sort((a, b) => a - b)
                      .map(day => dayNames[day])
                      .filter(name => name);

                    return selectedDays.map(dayName => (
                      <Tag key={dayName} color="blue" style={{ marginBottom: '4px' }}>
                        {dayName}
                      </Tag>
                    ));
                  })()}
                </Descriptions.Item>
              )}
              {/* 显示每月重复信息（仅当重复类型为每月时） */}
              {record.recurrenceType === 'MONTHLY' && (record.monthlyType || record.monthlyDay) && (
                <Descriptions.Item label="发生于" span={isMobileView ? 1 : 2}>
                  {(() => {
                    if (record.monthlyType === 'DAY_OF_MONTH' && record.monthlyDay) {
                      return (
                        <Tag color="green">
                          第{record.monthlyDay}天
                        </Tag>
                      );
                    } else if (record.monthlyType === 'DAY_OF_WEEK' && record.monthlyDay && record.monthlyWeekDay !== undefined) {
                      const weekOrder = {
                        1: '第一', 2: '第二', 3: '第三', 4: '第四', 5: '最后一'
                      };
                      const weekDayNames = {
                        0: '星期天', 1: '星期一', 2: '星期二', 3: '星期三',
                        4: '星期四', 5: '星期五', 6: '星期六'
                      };
                      return (
                        <Tag color="green">
                          {weekOrder[record.monthlyDay]}{weekDayNames[record.monthlyWeekDay]}
                        </Tag>
                      );
                    }
                    return null;
                  })()}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="结束类型">
                <Tag color={
                  record.endType === 'NO_END' ? 'green' :
                  record.endType === 'BY_DATE' ? 'orange' :
                  record.endType === 'BY_TIMES' ? 'purple' : 'default'
                }>
                  {record.endType === 'NO_END' ? '无结束时间' :
                   record.endType === 'BY_DATE' ? '按日期结束' :
                   record.endType === 'BY_TIMES' ? '按次数结束' :
                   record.endType}
                </Tag>
              </Descriptions.Item>
              {record.endType === 'BY_DATE' && record.endDateTime && (
                <Descriptions.Item label="结束日期">
                  {dayjs(record.endDateTime).format('YYYY-MM-DD')}
                </Descriptions.Item>
              )}
              {record.endType === 'BY_TIMES' && record.endTimes && (
                <Descriptions.Item label="总次数">
                  {record.endTimes} 次
                </Descriptions.Item>
              )}
            </Descriptions>
          </div>
        )}

        {/* 周期性会议实例列表 */}
        {occurrences.length > 0 && (
          <div style={{ marginTop: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
              <h4 style={{ margin: 0, color: '#1890ff' }}>
                <CalendarOutlined style={{ marginRight: '8px' }} />
                周期性会议实例 ({occurrences.length} 场)
              </h4>
              <Button
                type="primary"
                size="small"
                icon={<SettingOutlined />}
                onClick={() => handleBatchManageOccurrences(meetingId, occurrences, mainDetail)}
              >
                批量管理
              </Button>
            </div>
            <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
              {occurrences.map((occurrence, index) => (
                <div
                  key={occurrence.id || index}
                  style={{
                    marginBottom: '12px',
                    padding: '12px',
                    border: '1px solid #e8e8e8',
                    borderRadius: '6px',
                    backgroundColor: '#fff'
                  }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                    <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
                      第 {index + 1} 场会议
                    </span>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <Tag color={occurrence.occurrenceStatus === 'available' ? 'green' : 'orange'}>
                        {occurrence.occurrenceStatus || '未知'}
                      </Tag>
                      {/* 操作按钮组 */}
                      <div style={{ display: 'flex', gap: '4px' }}>
                        <Button
                          type="text"
                          size="small"
                          icon={<CopyOutlined />}
                          title="复制参会邀请"
                          onClick={() => handleCopyInvitation(occurrence, mainDetail)}
                          style={{ padding: '2px 4px' }}
                        />
                        <Button
                          type="text"
                          size="small"
                          icon={<EditOutlined />}
                          title="编辑"
                          onClick={() => handleEditOccurrence(meetingId, occurrence, mainDetail)}
                          style={{ padding: '2px 4px' }}
                        />
                        <Button
                          type="text"
                          size="small"
                          icon={<DeleteOutlined />}
                          title="删除"
                          danger
                          onClick={() => handleDeleteOccurrence(meetingId, occurrence)}
                          style={{ padding: '2px 4px' }}
                        />
                      </div>
                    </div>
                  </div>

                  <div style={{ fontSize: '13px', color: '#666' }}>
                    <div style={{ marginBottom: '4px' }}>
                      <strong>开始时间：</strong>
                      {occurrence.occurrenceStartTime ?
                        dayjs(occurrence.occurrenceStartTime).format('YYYY-MM-DD HH:mm:ss') :
                        '未设置'
                      }
                    </div>
                    <div style={{ marginBottom: '4px' }}>
                      <strong>持续时间：</strong>
                      {occurrence.duration || mainDetail.duration || '未设置'} 分钟
                    </div>
                    {occurrence.occurrenceId && (
                      <div style={{ marginBottom: '4px' }}>
                        <strong>Occurrence ID：</strong>
                        <Text copyable style={{ fontSize: '12px' }}>
                          {occurrence.occurrenceId}
                        </Text>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const columns = [
    {
      title: isMobileView ? '主题' : '会议主题',
      dataIndex: 'topic',
      key: 'topic',
      width: isMobileView ? 120 : 200,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {text}
        </span>
      ),
    },
    {
      title: isMobileView ? '会议号' : '会议号',
      dataIndex: 'zoomMeetingId',
      key: 'zoomMeetingId',
      width: isMobileView ? 80 : 100,
      render: (text) => (
        <span style={{
          fontSize: isMobileView ? '11px' : '14px',
          fontFamily: 'monospace'
        }}>
          {text || '-'}
        </span>
      ),
    },
    {
      title: isMobileView ? '姓名' : '用户姓名',
      dataIndex: 'creatorFullName',
      key: 'creatorFullName',
      width: isMobileView ? 60 : 80,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {text || '-'}
        </span>
      ),
    },
    {
      title: 'Zoom账号',
      dataIndex: 'hostEmail',
      key: 'hostEmail',
      width: isMobileView ? 100 : 140,
      ellipsis: true,
      render: (email, record) => {
        if (!email || email === '-') {
          return <span style={{ fontSize: isMobileView ? '11px' : '12px' }}>-</span>;
        }

        const zoomUserId = record.hostZoomUserId;
        return (
          <div style={{
            fontSize: isMobileView ? '11px' : '12px',
            wordBreak: 'break-all',
            whiteSpace: 'normal'
          }}>
            {zoomUserId ? (
              <a
                href={`http://localhost:3000/zoom-users/${zoomUserId}`}
                target="_blank"
                rel="noopener noreferrer"
                style={{ color: '#1890ff', textDecoration: 'none' }}
              >
                {email}
              </a>
            ) : (
              <span>{email}</span>
            )}
          </div>
        );
      },
    },
    {
      title: isMobileView ? '时间' : '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: isMobileView ? 90 : 320,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '10px' : '14px' }}>
          {dayjs(text).format(isMobileView ? 'MM-DD HH:mm' : 'YYYY-MM-DD HH:mm')}
        </span>
      ),
    },
    {
      title: isMobileView ? '时长' : '时长(分钟)',
      dataIndex: 'durationMinutes',
      key: 'durationMinutes',
      width: isMobileView ? 50 : 280,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '周期性',
      key: 'recurrence',
      width: isMobileView ? 60 : 90,
      render: (_, record) => {
        if (!record.isRecurring) {
          return (
            <Tag color="default" style={{ fontSize: isMobileView ? '9px' : '12px' }}>
              单次
            </Tag>
          );
        }

        // 周期性会议显示详细信息
        const recurrenceTypeMap = {
          'DAILY': '每天',
          'WEEKLY': '每周',
          'MONTHLY': '每月'
        };

        const endTypeMap = {
          'NO_END': '无限制',
          'BY_DATE': '至日期',
          'BY_TIMES': '次数限制'
        };

        const recurrenceText = recurrenceTypeMap[record.recurrenceType] || record.recurrenceType;
        const intervalText = record.repeatInterval > 1 ? `每${record.repeatInterval}天` : recurrenceText;
        const endText = endTypeMap[record.endType] || record.endType;

        return (
          <div>
            <Tag color="blue" style={{ fontSize: isMobileView ? '9px' : '12px', marginBottom: '2px' }}>
              周期性
            </Tag>
            {!isMobileView && (
              <div style={{ fontSize: '11px', color: '#666', lineHeight: '1.2' }}>
                {/* 优先显示后端生成的描述，如果没有则使用前端生成的描述 */}
                {record.recurrenceDescription || (
                  <div>
                    <div>{intervalText}</div>
                    <div>
                      {record.endType === 'BY_DATE' && record.endDateTime ?
                        `至${dayjs(record.endDateTime).format('MM-DD')}` :
                        record.endType === 'BY_TIMES' && record.endTimes ?
                        `${record.endTimes}次` :
                        endText
                      }
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: isMobileView ? 70 : 100,
      render: (status, record) => (
        <Select
          value={status}
          size="small"
          style={{ width: isMobileView ? 60 : 90, fontSize: isMobileView ? '11px' : '14px' }}
          onChange={(value) => handleStatusChange(record.id, value)}
        >
          <Option value="SCHEDULED">
            <Tag color="blue" style={{ fontSize: isMobileView ? '9px' : '12px' }}>
              {isMobileView ? '安排' : '已安排'}
            </Tag>
          </Option>
          <Option value="STARTED">
            <Tag color="green" style={{ fontSize: isMobileView ? '9px' : '12px' }}>
              {isMobileView ? '进行' : '进行中'}
            </Tag>
          </Option>
          <Option value="ENDED">
            <Tag color="default" style={{ fontSize: isMobileView ? '9px' : '12px' }}>
              {isMobileView ? '结束' : '已结束'}
            </Tag>
          </Option>
          <Option value="CANCELLED">
            <Tag color="red" style={{ fontSize: isMobileView ? '9px' : '12px' }}>
              {isMobileView ? '取消' : '已取消'}
            </Tag>
          </Option>
        </Select>
      ),
    },
    {
      title: isMobileView ? '来源' : '创建来源',
      dataIndex: 'creationSource',
      key: 'creationSource',
      width: isMobileView ? 50 : 70,
      render: (source) => {
        const sourceConfig = {
          ADMIN_PANEL: { color: 'blue', text: isMobileView ? '管理' : '管理台' },
          ZOOM_APP_WEBHOOK: { color: 'green', text: isMobileView ? 'Zoom' : 'Zoom应用' },
        };
        const config = sourceConfig[source] || { color: 'default', text: source };
        return (
          <Tag
            color={config.color}
            style={{ fontSize: isMobileView ? '9px' : '12px' }}
          >
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: isMobileView ? '创建时间' : '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: isMobileView ? 90 : 450,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '10px' : '14px' }}>
          {text ? dayjs(text).format(isMobileView ? 'MM-DD HH:mm' : 'YYYY-MM-DD HH:mm') : '-'}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 80 : 180,
      fixed: isMobileView ? 'right' : false,
      className: isMobileView ? 'action-column' : '',
      render: (_, record) => (
        <div style={{
          display: 'flex',
          flexDirection: isMobileView ? 'row' : 'column',
          gap: isMobileView ? '4px' : '8px',
          alignItems: isMobileView ? 'center' : 'flex-start'
        }}>
          {/* 第一行：复制参会邀请、复制主持信息 */}
          <div style={{
            display: 'flex',
            gap: isMobileView ? '2px' : '4px',
            flexWrap: isMobileView ? 'wrap' : 'nowrap'
          }}>
            <Button
              type="link"
              icon={<CopyOutlined />}
              onClick={() => handleCopyZoomInvitation(record)}
              size={isMobileView ? 'small' : 'middle'}
              style={{
                fontSize: isMobileView ? '10px' : '12px',
                padding: isMobileView ? '2px 4px' : '2px 8px',
                minWidth: isMobileView ? 'auto' : '80px'
              }}
              title="复制参会邀请信息"
            >
              {!isMobileView && '复制参会邀请'}
            </Button>
            <Button
              type="link"
              icon={<UserOutlined />}
              onClick={() => handleCopyHostInfo(record)}
              size={isMobileView ? 'small' : 'middle'}
              style={{
                fontSize: isMobileView ? '10px' : '12px',
                padding: isMobileView ? '2px 4px' : '2px 8px',
                minWidth: isMobileView ? 'auto' : '80px'
              }}
              title="复制主持信息"
            >
              {!isMobileView && '复制主持信息'}
            </Button>
          </div>

          {/* 第二行：查看报告、同步 */}
          <div style={{
            display: 'flex',
            gap: isMobileView ? '2px' : '4px',
            flexWrap: isMobileView ? 'wrap' : 'nowrap'
          }}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewMeetingReport(record)}
              size={isMobileView ? 'small' : 'middle'}
              style={{
                fontSize: isMobileView ? '10px' : '12px',
                padding: isMobileView ? '2px 4px' : '2px 8px',
                minWidth: isMobileView ? 'auto' : '80px'
              }}
              title="查看会议报告"
            >
              {!isMobileView && '会议报告'}
            </Button>
            <Button
              type="link"
              icon={<SyncOutlined />}
              onClick={() => handleSync(record.id)}
              size={isMobileView ? 'small' : 'middle'}
              style={{
                fontSize: isMobileView ? '10px' : '12px',
                padding: isMobileView ? '2px 4px' : '2px 8px',
                minWidth: isMobileView ? 'auto' : '80px'
              }}
            >
              {!isMobileView && '同步'}
            </Button>
          </div>

          {/* 第三行：删除 */}
          <div style={{
            display: 'flex',
            gap: isMobileView ? '2px' : '4px',
            flexWrap: isMobileView ? 'wrap' : 'nowrap'
          }}>
            <Popconfirm
              title="确定要删除这个会议吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                size={isMobileView ? 'small' : 'middle'}
                style={{
                  fontSize: isMobileView ? '10px' : '12px',
                  padding: isMobileView ? '2px 4px' : '2px 8px',
                  minWidth: isMobileView ? 'auto' : '80px'
                }}
              >
                {!isMobileView && '删除'}
              </Button>
            </Popconfirm>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div style={{ padding: isMobileView ? '12px' : '24px' }}>
      {/* 移动端滚动提示 */}
      {isMobileView && (
        <div className="mobile-scroll-hint" style={{ marginBottom: '16px' }}>
          👈 表格可左右滑动查看所有列信息（主题、会议号、姓名、Zoom账号、时间、时长、周期性、状态、来源、创建时间、操作）
        </div>
      )}

      <div style={{
        marginBottom: isMobileView ? 12 : 16,
        display: 'flex',
        flexDirection: isMobileView ? 'column' : 'row',
        justifyContent: 'space-between',
        gap: isMobileView ? '12px' : '0'
      }}>
        <h1 style={{ fontSize: isMobileView ? '20px' : '24px', marginBottom: isMobileView ? '8px' : '16px' }}>
          会议管理
        </h1>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreate}
          style={{ width: isMobileView ? '100%' : 'auto' }}
          size={isMobileView ? 'middle' : 'middle'}
        >
          安排会议
        </Button>
      </div>

      <Card size={isMobileView ? 'small' : 'default'}>
        <div data-page="meeting-list">
          <Table
            columns={columns}
            dataSource={meetings}
            rowKey="id"
            loading={loading}
            size={isMobileView ? 'small' : 'middle'}
            scroll={{ x: 'max-content' }}
            pagination={{
              showSizeChanger: !isMobileView,
              showQuickJumper: !isMobileView,
              showTotal: (total) => `共 ${total} 条记录`,
              simple: isMobileView,
              size: isMobileView ? 'small' : 'default',
            }}
            expandable={{
              expandedRowKeys,
              onExpand: (expanded, record) => {
                if (expanded) {
                  setExpandedRowKeys([...expandedRowKeys, record.id]);
                  // 加载Zoom会议详情
                  if (record.status === 'SCHEDULED' && record.zoomMeetingId) {
                    loadZoomMeetingDetail(record.id);
                  }
                } else {
                  setExpandedRowKeys(expandedRowKeys.filter(key => key !== record.id));
                }
              },
              expandedRowRender: renderExpandedRow,
              expandIcon: ({ expanded, onExpand, record }) => (
                <Tooltip title={expanded ? '收起详情' : '展开Zoom详情'}>
                  <Button
                    type="text"
                    size="small"
                    icon={expanded ? <DownOutlined /> : <RightOutlined />}
                    onClick={e => onExpand(record, e)}
                    disabled={record.status !== 'SCHEDULED' || !record.zoomMeetingId}
                  />
                </Tooltip>
              ),
              rowExpandable: (record) => record.status === 'SCHEDULED' && record.zoomMeetingId,
            }}
          />
        </div>
      </Card>

      <Modal
        title="安排会议"
        open={modalVisible}
        onCancel={() => !submitting && setModalVisible(false)}
        footer={null}
        width={700}
        closable={!submitting}
        maskClosable={!submitting}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          {/* 基本信息 */}
          <Form.Item
            name="topic"
            label="会议主题"
            rules={[{ required: true, message: '请输入会议主题' }]}
          >
            <Input placeholder="请输入会议主题" />
          </Form.Item>

          <Form.Item
            name="agenda"
            label="会议议程"
          >
            <TextArea
              rows={3}
              placeholder="请输入会议议程（可选）"
              showCount
              maxLength={2000}
            />
          </Form.Item>

          <Form.Item
            name="startTime"
            label="开始时间"
            rules={[{ required: true, message: '请选择开始时间' }]}
          >
            <DatePicker
              showTime
              format="YYYY-MM-DD HH:mm"
              style={{ width: '100%' }}
              placeholder="选择会议开始时间"
            />
          </Form.Item>
          <Form.Item label="持续时间">
            <Row gutter={8}>
              <Col span={12}>
                <Form.Item
                  name="durationHours"
                  rules={[{ required: true, message: '请选择小时' }]}
                >
                  <Select
                    placeholder="选择小时"
                    onChange={(value) => {
                      console.log('会议时长(小时)选择变化:', value, typeof value);
                    }}
                  >
                    {Array.from({ length: 25 }, (_, i) => (
                      <Option key={i} value={i}>
                        {i} 小时
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="durationMinutes"
                  rules={[{ required: true, message: '请选择分钟' }]}
                >
                  <Select
                    placeholder="选择分钟"
                    onChange={(value) => {
                      console.log('会议时长(分钟)选择变化:', value, typeof value);
                    }}
                  >
                    <Option value={0}>0 分钟</Option>
                    <Option value={15}>15 分钟</Option>
                    <Option value={30}>30 分钟</Option>
                    <Option value={45}>45 分钟</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Form.Item>
          <Form.Item
            name="timezone"
            label="时区"
          >
            <Select placeholder="选择时区" showSearch optionFilterProp="children">
              {/* UTC-12 到 UTC-10 */}
              <Option value="Pacific/Honolulu">Pacific/Honolulu - 夏威夷时间 (UTC-10)</Option>
              <Option value="America/Anchorage">America/Anchorage - 阿拉斯加时间 (UTC-9)</Option>

              {/* UTC-8 到 UTC-5 */}
              <Option value="America/Los_Angeles">America/Los_Angeles - 太平洋时间 (UTC-8)</Option>
              <Option value="America/Denver">America/Denver - 山地时间 (UTC-7)</Option>
              <Option value="America/Chicago">America/Chicago - 中部时间 (UTC-6)</Option>
              <Option value="America/New_York">America/New_York - 东部时间 (UTC-5)</Option>

              {/* UTC-4 到 UTC-3 */}
              <Option value="America/Caracas">America/Caracas - 委内瑞拉时间 (UTC-4)</Option>
              <Option value="America/Sao_Paulo">America/Sao_Paulo - 巴西时间 (UTC-3)</Option>

              {/* UTC+0 到 UTC+1 */}
              <Option value="UTC">UTC - 协调世界时 (UTC+0)</Option>
              <Option value="Europe/London">Europe/London - 格林威治标准时间 (UTC+0)</Option>
              <Option value="Europe/Paris">Europe/Paris - 中欧时间 (UTC+1)</Option>
              <Option value="Africa/Lagos">Africa/Lagos - 西非时间 (UTC+1)</Option>

              {/* UTC+2 到 UTC+3 */}
              <Option value="Europe/Helsinki">Europe/Helsinki - 东欧时间 (UTC+2)</Option>
              <Option value="Africa/Cairo">Africa/Cairo - 开罗时间 (UTC+2)</Option>
              <Option value="Europe/Moscow">Europe/Moscow - 莫斯科时间 (UTC+3)</Option>
              <Option value="Africa/Nairobi">Africa/Nairobi - 东非时间 (UTC+3)</Option>

              {/* UTC+4 到 UTC+5:30 */}
              <Option value="Asia/Dubai">Asia/Dubai - 阿联酋时间 (UTC+4)</Option>
              <Option value="Asia/Kolkata">Asia/Kolkata - 印度标准时间 (UTC+5:30)</Option>

              {/* UTC+7 到 UTC+8 */}
              <Option value="Asia/Bangkok">Asia/Bangkok - 东南亚时间 (UTC+7)</Option>
              <Option value="Asia/Shanghai">Asia/Shanghai - 中国标准时间 (UTC+8)</Option>
              <Option value="Australia/Perth">Australia/Perth - 澳大利亚西部时间 (UTC+8)</Option>

              {/* UTC+9 到 UTC+9:30 */}
              <Option value="Asia/Tokyo">Asia/Tokyo - 日本标准时间 (UTC+9)</Option>
              <Option value="Australia/Adelaide">Australia/Adelaide - 澳大利亚中部时间 (UTC+9:30)</Option>

              {/* UTC+10 到 UTC+12 */}
              <Option value="Australia/Sydney">Australia/Sydney - 澳大利亚东部时间 (UTC+10)</Option>
              <Option value="Pacific/Auckland">Pacific/Auckland - 新西兰时间 (UTC+12)</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="password"
            label="会议密码"
          >
            <Input
              placeholder="设置会议密码（可选）"
              maxLength={10}
              value={passwordValue}
              onChange={(e) => {
                const value = e.target.value;
                setPasswordValue(value);
                form.setFieldValue('password', value);
              }}
            />
            <div style={{ marginTop: 8 }}>
              <span style={{ fontSize: '12px', color: '#666', marginRight: 8 }}>快速选择：</span>
              {['2025', '123', '123456', '666', '888', '666666'].map(pwd => (
                <Button
                  key={pwd}
                  size="small"
                  type="text"
                  style={{
                    padding: '2px 6px',
                    height: 'auto',
                    fontSize: '12px',
                    marginRight: 4,
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px'
                  }}
                  onClick={() => {
                    console.log('点击快速选择密码:', pwd);
                    // 同时更新状态和表单值
                    setPasswordValue(pwd);
                    form.setFieldValue('password', pwd);
                    console.log('设置后的表单值:', form.getFieldsValue());
                  }}
                >
                  {pwd}
                </Button>
              ))}
            </div>
          </Form.Item>

          <Form.Item
            name="type"
            label="会议类型"
          >
            <Select placeholder="选择会议类型">
              <Option value="INSTANT">即时会议</Option>
              <Option value="SCHEDULED">预定会议</Option>
              <Option value="RECURRING_NO_FIXED_TIME">周期性会议(无固定时间)</Option>
              <Option value="RECURRING_FIXED_TIME">周期性会议(固定时间)</Option>
            </Select>
          </Form.Item>

          {/* 周期性会议设置 */}
          <div style={{ marginBottom: '16px' }}>
            <Checkbox
              checked={isRecurring}
              onChange={(e) => {
                const checked = e.target.checked;
                console.log('周期性会议复选框点击:', checked);
                setIsRecurring(checked);
                form.setFieldsValue({ isRecurring: checked });
                // 更新周期描述
                setTimeout(updateRecurrenceDescription, 100);
              }}
              style={{ fontSize: '16px', fontWeight: 'bold' }}
            >
              周期性会议
            </Checkbox>
            {isRecurring && recurrenceDescription && (
              <div style={{ fontSize: '12px', color: '#666', marginTop: 4, marginLeft: 24 }}>
                {recurrenceDescription}
              </div>
            )}
            {/* 隐藏的表单项用于数据提交 */}
            <Form.Item name="isRecurring" hidden>
              <input type="hidden" />
            </Form.Item>
          </div>

          {/* 周期性会议详细设置 */}
          {isRecurring && (
            <>
              <div style={{ marginLeft: 24, marginBottom: 16 }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="recurrenceType"
                      label="周期性会议"
                      rules={[{ required: isRecurring, message: '请选择重复类型' }]}
                    >
                      <Select
                        placeholder="选择重复类型"
                        onChange={(value) => {
                          setRecurrenceType(value);
                          setTimeout(updateRecurrenceDescription, 100);
                        }}
                      >
                        <Option value="DAILY">每天</Option>
                        <Option value="WEEKLY">每周</Option>
                        <Option value="MONTHLY">每月</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="repeatInterval"
                      label="重复间隔"
                      rules={[{ required: isRecurring, message: '请选择重复间隔' }]}
                    >
                      <Select
                        placeholder="选择间隔"
                        style={{ width: '100%' }}
                        onChange={(value) => {
                          console.log('重复间隔选择变化:', value, typeof value);
                          form.setFieldValue('repeatInterval', value);
                          setTimeout(updateRecurrenceDescription, 100);
                        }}
                      >
                        {Array.from({ length: 30 }, (_, i) => {
                          const interval = i + 1;
                          let unit = '';
                          if (recurrenceType === 'DAILY') {
                            unit = '天';
                          } else if (recurrenceType === 'WEEKLY') {
                            unit = '周';
                          } else if (recurrenceType === 'MONTHLY') {
                            unit = '个月';
                          }
                          return (
                            <Option key={interval} value={interval}>
                              每 {interval} {unit}
                            </Option>
                          );
                        })}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                {/* 每周时显示星期几选择 */}
                {recurrenceType === 'WEEKLY' && (
                  <Form.Item
                    name="weeklyDays"
                    label="发生于"
                    rules={[{ required: true, message: '请选择至少一个星期' }]}
                  >
                    <Checkbox.Group
                      style={{ width: '100%' }}
                      onChange={() => setTimeout(updateRecurrenceDescription, 100)}
                    >
                      <Row>
                        <Col span={8}>
                          <Checkbox value={1}>星期一</Checkbox>
                        </Col>
                        <Col span={8}>
                          <Checkbox value={2}>星期二</Checkbox>
                        </Col>
                        <Col span={8}>
                          <Checkbox value={3}>星期三</Checkbox>
                        </Col>
                        <Col span={8}>
                          <Checkbox value={4}>星期四</Checkbox>
                        </Col>
                        <Col span={8}>
                          <Checkbox value={5}>星期五</Checkbox>
                        </Col>
                        <Col span={8}>
                          <Checkbox value={6}>星期六</Checkbox>
                        </Col>
                        <Col span={8}>
                          <Checkbox value={0}>星期日</Checkbox>
                        </Col>
                      </Row>
                    </Checkbox.Group>
                  </Form.Item>
                )}

                {/* 每月时显示月度重复设置 */}
                {recurrenceType === 'MONTHLY' && (
                  <Form.Item label="发生于">
                    <Form.Item
                      name="monthlyType"
                      noStyle
                      rules={[{ required: true, message: '请选择月度重复方式' }]}
                      initialValue="DAY_OF_MONTH"
                    >
                      <Radio.Group
                        onChange={(e) => {
                          const newType = e.target.value;
                          setMonthlyType(newType);

                          // 清除另一种方式的表单值
                          if (newType === 'DAY_OF_MONTH') {
                            form.setFieldsValue({ monthlyWeekDay: undefined });
                          } else if (newType === 'DAY_OF_WEEK') {
                            // DAY_OF_WEEK模式下，monthlyDay字段用于存储"第几个"
                            // 不需要清除，因为两种模式都使用这个字段
                          }

                          setTimeout(updateRecurrenceDescription, 100);
                        }}
                      >
                        <Space direction="vertical" style={{ width: '100%' }}>
                          {/* 每月第几天选项 */}
                          <div>
                            <Radio value="DAY_OF_MONTH">每月第几天</Radio>
                            <div style={{
                              opacity: monthlyType === 'DAY_OF_MONTH' ? 1 : 0.6,
                              marginLeft: '24px',
                              marginTop: '8px'
                            }}>
                              <Space>
                                <span>第</span>
                                <Form.Item
                                  name="monthlyDay"
                                  noStyle
                                  rules={monthlyType === 'DAY_OF_MONTH' ? [{ required: true, message: '请选择日期' }] : []}
                                >
                                  <Select
                                    placeholder="选择日期"
                                    style={{ width: '100px' }}
                                    disabled={monthlyType !== 'DAY_OF_MONTH'}
                                    onChange={() => setTimeout(updateRecurrenceDescription, 100)}
                                  >
                                    {Array.from({ length: 31 }, (_, i) => (
                                      <Option key={i + 1} value={i + 1}>
                                        {i + 1}
                                      </Option>
                                    ))}
                                  </Select>
                                </Form.Item>
                                <span>天</span>
                              </Space>
                            </div>
                          </div>

                          {/* 每月第几个星期几选项 */}
                          <div style={{ marginTop: '16px' }}>
                            <Radio value="DAY_OF_WEEK">每月第几个星期几</Radio>
                            <div style={{
                              opacity: monthlyType === 'DAY_OF_WEEK' ? 1 : 0.6,
                              marginLeft: '24px',
                              marginTop: '8px'
                            }}>
                              <Space>
                                <span>第</span>
                                <Form.Item
                                  name="monthlyDay"
                                  noStyle
                                  rules={monthlyType === 'DAY_OF_WEEK' ? [{ required: true, message: '请选择第几个' }] : []}
                                >
                                  <Select
                                    placeholder="选择第几个"
                                    style={{ width: '100px' }}
                                    disabled={monthlyType !== 'DAY_OF_WEEK'}
                                    onChange={() => setTimeout(updateRecurrenceDescription, 100)}
                                  >
                                    <Option value={1}>第一</Option>
                                    <Option value={2}>第二</Option>
                                    <Option value={3}>第三</Option>
                                    <Option value={4}>第四</Option>
                                    <Option value={5}>最后一</Option>
                                  </Select>
                                </Form.Item>
                                <Form.Item
                                  name="monthlyWeekDay"
                                  noStyle
                                  rules={monthlyType === 'DAY_OF_WEEK' ? [{ required: true, message: '请选择星期几' }] : []}
                                >
                                  <Select
                                    placeholder="选择星期几"
                                    style={{ width: '100px', marginLeft: '8px' }}
                                    disabled={monthlyType !== 'DAY_OF_WEEK'}
                                    onChange={() => setTimeout(updateRecurrenceDescription, 100)}
                                  >
                                    <Option value={0}>星期天</Option>
                                    <Option value={1}>星期一</Option>
                                    <Option value={2}>星期二</Option>
                                    <Option value={3}>星期三</Option>
                                    <Option value={4}>星期四</Option>
                                    <Option value={5}>星期五</Option>
                                    <Option value={6}>星期六</Option>
                                  </Select>
                                </Form.Item>
                              </Space>
                            </div>
                          </div>
                        </Space>
                      </Radio.Group>
                    </Form.Item>
                  </Form.Item>
                )}

                <Form.Item
                  name="endType"
                  label="结束日期"
                  rules={[{ required: isRecurring, message: '请选择结束类型' }]}
                >
                  <Radio.Group onChange={() => setTimeout(updateRecurrenceDescription, 100)}>
                    <Radio value="NO_END">无结束时间</Radio>
                    <Radio value="BY_DATE">直到</Radio>
                    <Radio value="BY_TIMES">个次数</Radio>
                  </Radio.Group>
                </Form.Item>

                <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                  prevValues.endType !== currentValues.endType
                }>
                  {({ getFieldValue }) => {
                    const endType = getFieldValue('endType');
                    if (endType === 'BY_DATE') {
                      return (
                        <Form.Item
                          name="endDateTime"
                          rules={[{ required: true, message: '请选择结束日期' }]}
                        >
                          <DatePicker
                            format="YYYY-MM-DD"
                            style={{ width: 200, marginLeft: 24 }}
                            placeholder="选择结束日期"
                            onChange={() => setTimeout(updateRecurrenceDescription, 100)}
                          />
                        </Form.Item>
                      );
                    }
                    if (endType === 'BY_TIMES') {
                      return (
                        <Row gutter={8} style={{ marginLeft: 24 }}>
                          <Col span={8}>
                            <Form.Item
                              name="endTimes"
                              rules={[{ required: true, message: '请输入次数' }]}
                            >
                              <Select
                                placeholder="选择次数"
                                onChange={() => setTimeout(updateRecurrenceDescription, 100)}
                              >
                                {Array.from({ length: 50 }, (_, i) => (
                                  <Option key={i + 1} value={i + 1}>
                                    {i + 1}
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col span={4}>
                            <span style={{ lineHeight: '32px' }}>个次数</span>
                          </Col>
                        </Row>
                      );
                    }
                    return null;
                  }}
                </Form.Item>
              </div>
            </>
          )}

          <Divider orientation="left">会议设置</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="joinBeforeHost"
                label="允许参会者在主持人之前加入"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="muteUponEntry"
                label="参会者加入时静音"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="waitingRoom"
                label="启用等候室"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="languageInterpretation"
                label="启用语言传译"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">Zoom账号</Divider>

          <Form.Item
            name="zoomUserId"
            label="选择Zoom账号"
            rules={[{ required: true, message: '请选择Zoom账号' }]}
          >
            <Select
              placeholder="请输入邮箱或姓名搜索Zoom账号"
              showSearch
              allowClear
              loading={zoomUserSearchLoading}
              filterOption={false}
              onSearch={handleZoomUserSearch}
              optionLabelProp="label"
              notFoundContent={zoomUserSearchLoading ? '搜索中...' : '没有找到匹配的Zoom账号'}
              dropdownRender={(menu) => (
                <div>
                  {menu}
                  <div style={{
                    padding: '8px',
                    borderTop: '1px solid #f0f0f0',
                    color: '#999',
                    fontSize: '12px'
                  }}>
                    <div>输入至少2个字符开始搜索，仅显示LICENSED类型的PUBLIC_HOST账号</div>
                  </div>
                </div>
              )}
            >
              {zoomUsers.map(user => {
                const hasSystemUser = user.user && user.user.id;
                const displayName = user.displayName || `${user.firstName} ${user.lastName}`;
                return (
                  <Option
                    key={user.id}
                    value={user.id}
                    label={`${displayName} (${user.email})`}
                  >
                    <div>
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <span>{displayName}</span>
                        {hasSystemUser && (
                          <span style={{ color: '#52c41a', fontSize: '12px' }}>✓ 已关联</span>
                        )}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {user.email} - {user.zoomAuth?.accountName}
                        {hasSystemUser && (
                          <span style={{ marginLeft: 8, color: '#52c41a' }}>
                            (关联用户: {user.user.fullName})
                          </span>
                        )}
                      </div>
                    </div>
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={submitting}
                disabled={submitting}
                icon={submitting ? null : <PlusOutlined />}
              >
                {submitting ? '正在安排会议...' : '安排会议'}
              </Button>
              <Button
                onClick={() => setModalVisible(false)}
                disabled={submitting}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑会议实例模态框 */}
      <EditOccurrenceModal
        visible={editOccurrenceVisible}
        onCancel={() => setEditOccurrenceVisible(false)}
        onSuccess={handleEditSuccess}
        meetingId={currentEditMeetingId}
        occurrence={currentEditOccurrence}
        mainDetail={currentMainDetail}
      />

      {/* 批量管理会议实例模态框 */}
      <BatchOccurrenceModal
        visible={batchOccurrenceVisible}
        onCancel={() => setBatchOccurrenceVisible(false)}
        onSuccess={handleEditSuccess}
        meetingId={currentEditMeetingId}
        occurrences={currentOccurrences}
        mainDetail={currentMainDetail}
      />

      {/* 会议报告详情弹窗 */}
      <MeetingReportModal
        visible={reportModalVisible}
        onCancel={() => {
          setReportModalVisible(false);
          setCurrentMeetingForReport(null);
        }}
        meeting={currentMeetingForReport}
        isMobileView={isMobileView}
      />
    </div>
  );
};

export default MeetingList;
