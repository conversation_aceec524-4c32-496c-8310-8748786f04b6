import React, { useState } from 'react';
import {
  Card, Form, Input, Button, Select, DatePicker, Table, Modal, 
  message, Tag, Space, Tooltip, Typography, Row, Col, Spin
} from 'antd';
import { SearchOutlined, DownloadOutlined, EyeOutlined, ReloadOutlined } from '@ant-design/icons';
import api from '../services/api';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Text, Paragraph } = Typography;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

/**
 * 日志检索页面
 * 提供TraceId日志检索功能，支持多维度搜索和调用链查看
 */
const LogSearch = () => {
  const [searchForm] = Form.useForm();
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedLog, setSelectedLog] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 50,
    total: 0
  });

  // 监听窗口大小变化
  React.useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 搜索日志
  const handleSearch = async (values) => {
    setLoading(true);
    try {
      const searchParams = {
        ...values,
        timeRange: values.timeRange ? [
          values.timeRange[0].format('YYYY-MM-DD HH:mm:ss'),
          values.timeRange[1].format('YYYY-MM-DD HH:mm:ss')
        ] : null,
        page: 1,
        size: pagination.pageSize
      };

      const response = await api.post('/logs/search', searchParams);
      setSearchResults(response.data.data || []);
      setPagination({
        ...pagination,
        current: 1,
        total: response.data.total || 0
      });
      
      message.success(`找到 ${response.data.total || 0} 条日志记录`);
    } catch (error) {
      console.error('搜索失败:', error);
      message.error('搜索失败: ' + (error.response?.data?.message || error.message));
      setSearchResults([]);
      setPagination({ ...pagination, current: 1, total: 0 });
    } finally {
      setLoading(false);
    }
  };

  // 查看完整调用链
  const handleViewTraceChain = async (traceId) => {
    setLoading(true);
    try {
      const response = await api.get(`/logs/trace/${traceId}`);
      setSearchResults(response.data || []);
      setPagination({
        ...pagination,
        current: 1,
        total: response.data?.length || 0
      });
      message.success(`找到 ${response.data?.length || 0} 条调用链记录`);
    } catch (error) {
      console.error('获取调用链失败:', error);
      message.error('获取调用链失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 导出日志
  const handleExport = async (traceId) => {
    try {
      const response = await api.get(`/logs/export?traceId=${traceId}`, {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `logs_${traceId}.txt`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      message.success('日志导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 清空搜索结果
  const handleClear = () => {
    searchForm.resetFields();
    setSearchResults([]);
    setPagination({ current: 1, pageSize: 50, total: 0 });
  };

  // 表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: isMobileView ? 120 : 180,
      render: (text) => (
        <Text code style={{ fontSize: isMobileView ? '10px' : '12px' }}>
          {isMobileView ? text.substring(5, 16) : text}
        </Text>
      )
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: isMobileView ? 60 : 80,
      render: (level) => {
        const colors = {
          ERROR: 'red',
          WARN: 'orange',
          INFO: 'blue',
          DEBUG: 'gray'
        };
        return (
          <Tag
            color={colors[level] || 'default'}
            style={{ fontSize: isMobileView ? '9px' : '11px' }}
          >
            {isMobileView ? level.substring(0, 1) : level}
          </Tag>
        );
      }
    },
    {
      title: 'TraceId',
      dataIndex: 'traceId',
      key: 'traceId',
      width: isMobileView ? 120 : 200,
      render: (traceId) => (
        <Tooltip title="点击查看完整调用链">
          <Button
            type="link"
            size="small"
            onClick={() => handleViewTraceChain(traceId)}
            style={{
              padding: 0,
              fontSize: isMobileView ? '10px' : '12px',
              maxWidth: isMobileView ? '100px' : '180px'
            }}
          >
            <Text ellipsis style={{ fontSize: isMobileView ? '10px' : '12px' }}>
              {isMobileView ? traceId.substring(0, 8) + '...' : traceId}
            </Text>
          </Button>
        </Tooltip>
      )
    },
    // 移动端隐藏来源列
    ...(isMobileView ? [] : [{
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      width: 100,
      render: (source) => <Tag>{source || 'APPLICATION'}</Tag>
    }]),
    // 移动端隐藏Logger列
    ...(isMobileView ? [] : [{
      title: 'Logger',
      dataIndex: 'logger',
      key: 'logger',
      width: 150,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <Text style={{ fontSize: '12px' }}>{text}</Text>
        </Tooltip>
      )
    }]),
    {
      title: isMobileView ? '内容' : '日志内容',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <Text style={{ fontSize: isMobileView ? '10px' : '12px' }}>
            {isMobileView && text && text.length > 30 ? `${text.substring(0, 30)}...` : text}
          </Text>
        </Tooltip>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 80 : 120,
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedLog(record);
              setDetailModalVisible(true);
            }}
          >
            详情
          </Button>
          <Button 
            type="link" 
            size="small"
            icon={<DownloadOutlined />}
            onClick={() => handleExport(record.traceId)}
          >
            导出
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div className="log-search-container" style={{ padding: isMobileView ? '12px' : '24px' }}>
      {/* 搜索表单 */}
      <Card title="日志检索" style={{ marginBottom: 16 }}>
        <Form form={searchForm} layout="vertical" onFinish={handleSearch}>
          <Row gutter={16}>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="traceId" label="TraceId">
                <Input
                  placeholder="输入完整TraceId或部分关键字"
                  allowClear
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="timeRange" label="时间范围">
                <RangePicker
                  showTime
                  style={{ width: '100%' }}
                  placeholder={['开始时间', '结束时间']}
                />
              </Form.Item>
            </Col>
            <Col xs={12} sm={6} md={4}>
              <Form.Item name="logLevel" label="日志级别">
                <Select placeholder="选择级别" allowClear>
                  <Option value="ERROR">ERROR</Option>
                  <Option value="WARN">WARN</Option>
                  <Option value="INFO">INFO</Option>
                  <Option value="DEBUG">DEBUG</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={12} sm={6} md={4}>
              <Form.Item name="source" label="日志来源">
                <Select placeholder="选择来源" allowClear>
                  <Option value="APPLICATION">应用日志</Option>
                  <Option value="WEBHOOK">Webhook</Option>
                  <Option value="SCHEDULER">定时任务</Option>
                  <Option value="API">API调用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item name="keyword" label="关键字">
                <Input 
                  placeholder="搜索日志内容中的关键字" 
                  allowClear
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                icon={<SearchOutlined />}
              >
                搜索
              </Button>
              <Button 
                onClick={handleClear}
                icon={<ReloadOutlined />}
              >
                清空
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 搜索结果 */}
      <Card title={`搜索结果 (${pagination.total} 条)`}>
        <Spin spinning={loading}>
          <div data-page="log-search">
            <Table
              columns={columns}
              dataSource={searchResults}
              size={isMobileView ? 'small' : 'small'}
              pagination={{
                ...pagination,
                showSizeChanger: !isMobileView,
                showQuickJumper: !isMobileView,
                showTotal: (total, range) =>
                  isMobileView
                    ? `${range[0]}-${range[1]} / ${total}`
                    : `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                simple: isMobileView,
                size: isMobileView ? 'small' : 'default',
                onChange: (page, pageSize) => {
                  setPagination({ ...pagination, current: page, pageSize });
                }
              }}
              scroll={{ x: isMobileView ? 400 : 'max-content' }}
              rowKey={(record, index) => record.traceId + '_' + index}
            />
          </div>
        </Spin>
      </Card>

      {/* 日志详情模态框 */}
      <Modal
        title="日志详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedLog && (
          <div>
            <Paragraph>
              <Text strong>时间：</Text>
              <Text code>{selectedLog.timestamp}</Text>
            </Paragraph>
            <Paragraph>
              <Text strong>级别：</Text>
              <Tag color={selectedLog.level === 'ERROR' ? 'red' : 'blue'}>
                {selectedLog.level}
              </Tag>
            </Paragraph>
            <Paragraph>
              <Text strong>TraceId：</Text>
              <Text code>{selectedLog.traceId}</Text>
            </Paragraph>
            <Paragraph>
              <Text strong>线程：</Text>
              <Text code>{selectedLog.thread || 'N/A'}</Text>
            </Paragraph>
            <Paragraph>
              <Text strong>Logger：</Text>
              <Text code>{selectedLog.logger || 'N/A'}</Text>
            </Paragraph>
            <Paragraph>
              <Text strong>来源：</Text>
              <Tag>{selectedLog.source || 'APPLICATION'}</Tag>
            </Paragraph>
            <Paragraph>
              <Text strong>消息：</Text>
            </Paragraph>
            <Paragraph>
              <pre style={{ 
                background: '#f5f5f5', 
                padding: '12px', 
                borderRadius: '4px',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                maxHeight: '200px',
                overflow: 'auto'
              }}>
                {selectedLog.message}
              </pre>
            </Paragraph>
            {selectedLog.context && Object.keys(selectedLog.context).length > 0 && (
              <>
                <Paragraph>
                  <Text strong>上下文信息：</Text>
                </Paragraph>
                <Paragraph>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '12px', 
                    borderRadius: '4px',
                    maxHeight: '150px',
                    overflow: 'auto'
                  }}>
                    {JSON.stringify(selectedLog.context, null, 2)}
                  </pre>
                </Paragraph>
              </>
            )}
            {selectedLog.rawLine && (
              <>
                <Paragraph>
                  <Text strong>原始日志：</Text>
                </Paragraph>
                <Paragraph>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '12px', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word',
                    maxHeight: '200px',
                    overflow: 'auto'
                  }}>
                    {selectedLog.rawLine}
                  </pre>
                </Paragraph>
              </>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default LogSearch;
