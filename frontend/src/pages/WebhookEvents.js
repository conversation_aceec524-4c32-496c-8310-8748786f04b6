import React, { useState, useEffect } from 'react';
import { Table, Tag, Card, Statistic, Row, Col, Select, Button, message, Popconfirm, Space, Switch, Tooltip } from 'antd';
import { ReloadOutlined, SyncOutlined } from '@ant-design/icons';
import { webhookApi } from '../services/api';
import dayjs from 'dayjs';

const { Option } = Select;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const WebhookEvents = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    processed: 0,
    failed: 0,
    pending: 0,
  });
  const [selectedStatus, setSelectedStatus] = useState('ALL');
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastRefreshTime, setLastRefreshTime] = useState(null);

  useEffect(() => {
    loadEvents();
  }, [selectedStatus]); // eslint-disable-line react-hooks/exhaustive-deps

  // 自动刷新功能 - 每10秒刷新一次
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadEvents();
    }, 10000); // 10秒 = 10000毫秒

    return () => clearInterval(interval);
  }, [selectedStatus, autoRefresh]); // eslint-disable-line react-hooks/exhaustive-deps

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const loadEvents = async () => {
    try {
      setLoading(true);
      let response;
      if (selectedStatus === 'ALL') {
        response = await webhookApi.getWebhookEvents();
      } else {
        response = await webhookApi.getWebhookEventsByStatus(selectedStatus);
      }

      // 确保响应数据是数组格式
      const eventsData = Array.isArray(response?.data) ? response.data : [];
      setEvents(eventsData);

      // 计算统计数据
      let allEventsData;
      if (selectedStatus === 'ALL') {
        allEventsData = eventsData;
      } else {
        const allEventsResponse = await webhookApi.getWebhookEvents();
        allEventsData = Array.isArray(allEventsResponse?.data) ? allEventsResponse.data : [];
      }

      const statsData = {
        total: allEventsData.length,
        processed: allEventsData.filter(e => e.processingStatus === 'PROCESSED').length,
        failed: allEventsData.filter(e => e.processingStatus === 'FAILED').length,
        pending: allEventsData.filter(e => e.processingStatus === 'PENDING').length,
      };
      setStats(statsData);

      // 更新最后刷新时间
      setLastRefreshTime(new Date());
    } catch (error) {
      console.error('加载Webhook事件失败:', error);
      // 设置默认值以防止错误
      setEvents([]);
      setStats({ total: 0, processed: 0, failed: 0, pending: 0 });
    } finally {
      setLoading(false);
    }
  };

  // 重放事件
  const handleReplayEvent = async (eventId, eventType) => {
    try {
      const response = await webhookApi.replayWebhookEvent(eventId);
      if (response.data.success) {
        message.success(`事件重放成功: ${eventType}`);
        // 重新加载事件列表
        loadEvents();
      } else {
        message.error(`事件重放失败: ${response.data.message}`);
      }
    } catch (error) {
      console.error('重放事件失败:', error);
      message.error('重放事件失败，请稍后重试');
    }
  };

  // 手动刷新
  const handleManualRefresh = () => {
    loadEvents();
    message.success('数据已刷新');
  };

  // 切换自动刷新
  const handleAutoRefreshToggle = (checked) => {
    setAutoRefresh(checked);
    if (checked) {
      message.success('已开启自动刷新（每10秒）');
    } else {
      message.info('已关闭自动刷新');
    }
  };

  const columns = [
    {
      title: isMobileView ? '类型' : '事件类型',
      dataIndex: 'eventType',
      key: 'eventType',
      width: isMobileView ? 80 : 120,
      render: (type) => {
        const typeConfig = {
          'meeting.created': { color: 'blue', text: '会议创建' },
          'meeting.updated': { color: 'cyan', text: '会议更新' },
          'meeting.deleted': { color: 'red', text: '会议删除' },
          'meeting.started': { color: 'green', text: '会议开始' },
          'meeting.ended': { color: 'default', text: '会议结束' },
          'user.created': { color: 'purple', text: '用户创建' },
          'user.updated': { color: 'orange', text: '用户更新' },
          'user.deleted': { color: 'magenta', text: '用户删除' },
        };
        const config = typeConfig[type] || { color: 'default', text: type };
        return (
          <Tag
            color={config.color}
            style={{ fontSize: isMobileView ? '10px' : '12px' }}
          >
            {config.text}
          </Tag>
        );
      },
    },
    // 移动端隐藏 Zoom账号ID 列
    ...(isMobileView ? [] : [{
      title: 'Zoom账号ID',
      dataIndex: 'zoomAccountId',
      key: 'zoomAccountId',
      width: 120,
      render: (text) => (
        <span style={{ fontSize: '12px' }}>
          {text || '-'}
        </span>
      ),
    }]),
    {
      title: isMobileView ? '会议ID' : 'Zoom会议ID',
      dataIndex: 'zoomMeetingId',
      key: 'zoomMeetingId',
      width: isMobileView ? 100 : 120,
      render: (text) => (
        <span style={{
          fontSize: isMobileView ? '11px' : '12px',
          fontFamily: 'monospace'
        }}>
          {text || '-'}
        </span>
      ),
    },
    {
      title: isMobileView ? '状态' : '处理状态',
      dataIndex: 'processingStatus',
      key: 'processingStatus',
      width: isMobileView ? 70 : 100,
      render: (status) => {
        const statusConfig = {
          PENDING: { color: 'orange', text: '待处理' },
          PROCESSED: { color: 'green', text: '已处理' },
          FAILED: { color: 'red', text: '处理失败' },
          IGNORED: { color: 'default', text: '已忽略' },
        };
        const config = statusConfig[status] || { color: 'default', text: status };
        return (
          <Tag
            color={config.color}
            style={{ fontSize: isMobileView ? '10px' : '12px' }}
          >
            {config.text}
          </Tag>
        );
      },
    },
    // 移动端隐藏错误信息列
    ...(isMobileView ? [] : [{
      title: '错误信息',
      dataIndex: 'errorMessage',
      key: 'errorMessage',
      width: 150,
      render: (text) => text ? (
        <span style={{ color: 'red', fontSize: '12px' }}>
          {text.length > 50 ? `${text.substring(0, 50)}...` : text}
        </span>
      ) : '-',
    }]),
    {
      title: isMobileView ? '时间' : '接收时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: isMobileView ? 100 : 150,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '10px' : '12px' }}>
          {isMobileView
            ? dayjs(text).format('MM-DD HH:mm')
            : dayjs(text).format('YYYY-MM-DD HH:mm:ss')
          }
        </span>
      ),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
      sortOrder: 'descend',
      defaultSortOrder: 'descend',
    },
    // 移动端隐藏处理时间列
    ...(isMobileView ? [] : [{
      title: '处理时间',
      dataIndex: 'processedAt',
      key: 'processedAt',
      width: 150,
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
    }]),
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 80 : 120,
      render: (_, record) => (
        <Space size="small">
          <Popconfirm
            title="确定要重放此事件吗？"
            description={`将重新向后端发送 ${record.eventType} 事件`}
            onConfirm={() => handleReplayEvent(record.id, record.eventType)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="primary"
              size="small"
              icon={<ReloadOutlined />}
              title="重放事件"
            >
              {isMobileView ? '' : '重放'}
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const expandedRowRender = (record) => {
    let eventData;
    try {
      eventData = JSON.parse(record.eventData);
    } catch (e) {
      eventData = record.eventData;
    }

    return (
      <div style={{ margin: 0 }}>
        <h4>事件数据:</h4>
        <pre style={{ 
          background: '#f5f5f5', 
          padding: '12px', 
          borderRadius: '4px',
          fontSize: '12px',
          maxHeight: '300px',
          overflow: 'auto'
        }}>
          {typeof eventData === 'string' ? eventData : JSON.stringify(eventData, null, 2)}
        </pre>
      </div>
    );
  };

  return (
    <div>
      <h1>Webhook事件</h1>
      
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总事件数"
              value={stats.total}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已处理"
              value={stats.processed}
              valueStyle={{ color: '#3f8600' }}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="处理失败"
              value={stats.failed}
              valueStyle={{ color: '#cf1322' }}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待处理"
              value={stats.pending}
              valueStyle={{ color: '#d48806' }}
              loading={loading}
            />
          </Card>
        </Col>
      </Row>

      <div style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle" gutter={16}>
          <Col xs={24} sm={12} md={8}>
            <Space>
              <span>筛选状态:</span>
              <Select
                value={selectedStatus}
                onChange={setSelectedStatus}
                style={{ width: 120 }}
              >
                <Option value="ALL">全部</Option>
                <Option value="PENDING">待处理</Option>
                <Option value="PROCESSED">已处理</Option>
                <Option value="FAILED">处理失败</Option>
                <Option value="IGNORED">已忽略</Option>
              </Select>
            </Space>
          </Col>

          <Col xs={24} sm={12} md={8} style={{ textAlign: 'center' }}>
            <Space direction="vertical" size={4}>
              <div style={{ color: '#666', fontSize: '12px' }}>
                📅 事件按接收时间倒序排列，最新事件在前
              </div>
              {lastRefreshTime && (
                <div style={{ color: '#999', fontSize: '11px' }}>
                  最后刷新: {dayjs(lastRefreshTime).format('HH:mm:ss')}
                </div>
              )}
            </Space>
          </Col>

          <Col xs={24} sm={24} md={8} style={{ textAlign: 'right' }}>
            <Space>
              <Tooltip title="手动刷新数据">
                <Button
                  type="default"
                  icon={<SyncOutlined spin={loading} />}
                  onClick={handleManualRefresh}
                  loading={loading}
                  size="small"
                >
                  {!isMobileView && '刷新'}
                </Button>
              </Tooltip>

              <Tooltip title={autoRefresh ? '关闭自动刷新' : '开启自动刷新（每10秒）'}>
                <Space>
                  <Switch
                    checked={autoRefresh}
                    onChange={handleAutoRefreshToggle}
                    size="small"
                  />
                  {!isMobileView && (
                    <span style={{ fontSize: '12px', color: '#666' }}>
                      自动刷新
                    </span>
                  )}
                </Space>
              </Tooltip>
            </Space>
          </Col>
        </Row>
      </div>

      <div data-page="webhook-events">
        <Table
          columns={columns}
          dataSource={events}
          rowKey="id"
          loading={loading}
          size={isMobileView ? 'small' : 'middle'}
          expandable={{
            expandedRowRender,
            rowExpandable: (record) => !!record.eventData,
          }}
          pagination={{
            showSizeChanger: !isMobileView,
            showQuickJumper: !isMobileView,
            showTotal: (total, range) =>
              isMobileView
                ? `${range[0]}-${range[1]} / ${total}`
                : `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            simple: isMobileView,
            size: isMobileView ? 'small' : 'default'
          }}
          scroll={{ x: isMobileView ? 400 : 'auto' }}
        />
      </div>
    </div>
  );
};

export default WebhookEvents;
