import React, { useState, useEffect } from 'react';
import {
    Card,
    Table,
    Button,
    Input,
    Select,
    DatePicker,
    Space,
    Tag,
    Statistic,
    Row,
    Col,
    Modal,
    message,
    Tabs,
    Tooltip,
    Badge,
    Typography,
    Switch
} from 'antd';
import {
    SearchOutlined,
    ReloadOutlined,
    StopOutlined,
    EyeOutlined,
    CalendarOutlined,
    ClockCircleOutlined,
    UserOutlined,
    VideoCameraOutlined,
    SyncOutlined
} from '@ant-design/icons';
import api from '../services/api';
import dayjs from 'dayjs';
import MeetingReportModal from '../components/MeetingReportModal';
import MeetingReportStatistics from '../components/MeetingReportStatistics';
import './ZoomMeetingDashboard.css';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Text } = Typography;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const ZoomMeetingDashboard = () => {
    const [loading, setLoading] = useState(false);
    const [activeMeetings, setActiveMeetings] = useState([]);
    const [historyMeetings, setHistoryMeetings] = useState([]);
    const [dashboardData, setDashboardData] = useState({});
    const [activeFilters, setActiveFilters] = useState({
        billingMode: '',
        keyword: ''
    });
    const [historyFilters, setHistoryFilters] = useState({
        billingMode: '',
        keyword: '',
        dateRange: null
    });
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });
    const [activeTab, setActiveTab] = useState('active');
    const [endingMeetings, setEndingMeetings] = useState(new Set()); // 正在结束的会议ID集合
    const [isMobileView, setIsMobileView] = useState(isMobile());
    const [autoRefresh, setAutoRefresh] = useState(true);
    const [lastRefreshTime, setLastRefreshTime] = useState(null);
    const [reportModalVisible, setReportModalVisible] = useState(false);
    const [currentMeeting, setCurrentMeeting] = useState(null);

    // 获取看板数据
    const fetchDashboardData = async () => {
        try {
            console.log('🔄 获取Zoom会议看板数据...');
            const response = await api.get('/zoom-meetings/dashboard');
            console.log('✅ 看板数据响应:', response.data);

            if (response.data.success) {
                setDashboardData(response.data.data);
                setLastRefreshTime(new Date());
            } else {
                console.warn('⚠️ 看板数据获取成功但返回失败状态:', response.data);
            }
        } catch (error) {
            console.error('❌ 获取看板数据失败:', error);
            const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const errorPrefix = isMobileDevice ? '[移动端]' : '[桌面端]';
            message.error(`${errorPrefix} 获取看板数据失败: ${error.message}`);
        }
    };

    // 获取活跃会议列表
    const fetchActiveMeetings = async (page = 1, size = 10) => {
        setLoading(true);
        try {
            const params = {
                page: page - 1,
                size,
                ...activeFilters
            };
            
            const response = await api.get('/zoom-meetings/active', { params });
            if (response.data.success) {
                setActiveMeetings(response.data.data.content);
                setPagination({
                    current: page,
                    pageSize: size,
                    total: response.data.data.totalElements
                });
            }
        } catch (error) {
            message.error('获取活跃会议失败');
            console.error('获取活跃会议失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 获取历史会议列表
    const fetchHistoryMeetings = async (page = 1, size = 10) => {
        setLoading(true);
        try {
            const params = {
                page: page - 1,
                size,
                ...historyFilters
            };
            
            if (historyFilters.dateRange) {
                params.startDate = historyFilters.dateRange[0].toISOString();
                params.endDate = historyFilters.dateRange[1].toISOString();
            }
            
            const response = await api.get('/zoom-meetings/history', { params });
            if (response.data.success) {
                setHistoryMeetings(response.data.data.content);
                setPagination({
                    current: page,
                    pageSize: size,
                    total: response.data.data.totalElements
                });
            }
        } catch (error) {
            message.error('获取历史会议失败');
            console.error('获取历史会议失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 结束会议
    const endMeeting = async (meetingId, meetingStatus = 'USING') => {
        // 防重复操作检查
        if (endingMeetings.has(meetingId)) {
            message.warning('会议正在处理中，请稍候...');
            return;
        }

        const actionText = meetingStatus === 'PENDING' ? '取消' : '结束';
        const loadingMessage = `正在${actionText}会议...`;

        // 添加到正在处理的集合
        setEndingMeetings(prev => new Set([...prev, meetingId]));

        // 显示加载提示
        const hideLoading = message.loading(loadingMessage, 0);

        try {
            const response = await api.post(`/zoom-meetings/${meetingId}/end`);
            hideLoading();

            if (response.data.success) {
                message.success(`会议已${actionText}`);
                // 刷新数据
                await Promise.all([
                    fetchActiveMeetings(),
                    fetchDashboardData()
                ]);
            } else {
                const errorMsg = response.data.message || `${actionText}会议失败`;
                message.error(errorMsg);
            }
        } catch (error) {
            hideLoading();

            // 详细错误处理
            let errorMessage = `${actionText}会议失败`;

            if (error.response) {
                // 服务器响应错误
                const status = error.response.status;
                const data = error.response.data;

                if (status === 404) {
                    errorMessage = '会议记录不存在';
                } else if (status === 400) {
                    errorMessage = data.message || '请求参数错误';
                } else if (status === 500) {
                    errorMessage = '服务器内部错误，请稍后重试';
                } else if (data && data.message) {
                    errorMessage = data.message;
                }
            } else if (error.request) {
                // 网络错误
                errorMessage = '网络连接失败，请检查网络后重试';
            }

            message.error(errorMessage);
            console.error(`${actionText}会议失败:`, error);
        } finally {
            // 从正在处理的集合中移除
            setEndingMeetings(prev => {
                const newSet = new Set(prev);
                newSet.delete(meetingId);
                return newSet;
            });
        }
    };

    // 查看会议报告
    const handleViewMeetingReport = (meeting) => {
        setCurrentMeeting(meeting);
        setReportModalVisible(true);
    };

    // 获取计费模式标签
    const getBillingModeTag = (mode) => {
        if (mode === 'LONG') {
            return <Tag color="blue">按时段</Tag>;
        } else if (mode === 'BY_TIME') {
            return <Tag color="green">按时长</Tag>;
        } else if (mode === 'FREE') {
            return <Tag color="gray">免费</Tag>;
        }
        return <Tag>未知</Tag>;
    };

    // 获取会议来源标签
    const getCreationSourceTag = (source) => {
        const sourceMap = {
            'PMI_MEETING': { color: 'purple', text: 'PMI会议' },
            'ADMIN_PANEL': { color: 'blue', text: '管理台' },
            'ZOOM_CLIENT': { color: 'green', text: 'Zoom端' },
            'UNKNOWN': { color: 'default', text: '未知' }
        };
        const config = sourceMap[source] || { color: 'default', text: source || '未知' };
        return <Tag color={config.color}>{config.text}</Tag>;
    };

    // 获取会议状态标签
    const getStatusTag = (status) => {
        const statusMap = {
            'PENDING': { color: 'orange', text: '待开启' },
            'USING': { color: 'green', text: '进行中' },
            'ENDED': { color: 'red', text: '已结束' },
            'SETTLED': { color: 'blue', text: '已结算' }
        };
        const config = statusMap[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
    };

    // 活跃会议表格列
    const activeMeetingColumns = [
        {
            title: isMobileView ? '主题' : '会议主题',
            dataIndex: 'topic',
            key: 'topic',
            width: isMobileView ? 120 : 200,
            ellipsis: true,
            render: (text) => (
                <span style={{ fontSize: isMobileView ? '12px' : '14px' }}>
                    {text || '无主题'}
                </span>
            )
        },
        {
            title: isMobileView ? '会议号' : '会议号',
            dataIndex: 'zoomMeetingId',
            key: 'zoomMeetingId',
            width: isMobileView ? 100 : 120,
            render: (text) => (
                <span style={{
                    fontSize: isMobileView ? '11px' : '14px',
                    fontFamily: 'monospace'
                }}>
                    {text}
                </span>
            )
        },
        {
            title: '姓名',
            dataIndex: 'assignedUserFullName',
            key: 'assignedUserFullName',
            width: isMobileView ? 80 : 120,
            ellipsis: true,
            render: (text) => (
                <span style={{
                    fontSize: isMobileView ? '11px' : '14px'
                }}>
                    {text || '-'}
                </span>
            )
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: isMobileView ? 60 : 80,
            render: (status) => (
                <div style={{ fontSize: isMobileView ? '10px' : '12px' }}>
                    {getStatusTag(status)}
                </div>
            )
        },
        {
            title: isMobileView ? '来源' : '会议来源',
            dataIndex: 'creationSource',
            key: 'creationSource',
            width: isMobileView ? 60 : 100,
            render: (source) => (
                <div style={{ fontSize: isMobileView ? '10px' : '12px' }}>
                    {getCreationSourceTag(source)}
                </div>
            )
        },
        {
            title: isMobileView ? '计费' : '计费模式',
            dataIndex: 'billingMode',
            key: 'billingMode',
            width: isMobileView ? 60 : 100,
            render: (mode) => (
                <div style={{ fontSize: isMobileView ? '10px' : '12px' }}>
                    {getBillingModeTag(mode)}
                </div>
            )
        },
        {
            title: isMobileView ? '开始' : '开始时间',
            dataIndex: 'startTime',
            key: 'startTime',
            width: isMobileView ? 80 : 160,
            render: (time) => (
                <span style={{ fontSize: isMobileView ? '10px' : '14px' }}>
                    {time ? (isMobileView ?
                        dayjs(time).format('MM-DD HH:mm') :
                        dayjs(time).format('YYYY-MM-DD HH:mm:ss')
                    ) : '-'}
                </span>
            )
        },
        {
            title: isMobileView ? '时长' : '持续时长',
            dataIndex: 'startTime',
            key: 'duration',
            width: isMobileView ? 60 : 100,
            render: (startTime) => {
                if (!startTime) return '-';
                const duration = dayjs().diff(dayjs(startTime), 'minute');
                return (
                    <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
                        {`${duration} 分钟`}
                    </span>
                );
            }
        },
        {
            title: isMobileView ? '计费' : '已计费',
            dataIndex: 'billedMinutes',
            key: 'billedMinutes',
            width: isMobileView ? 50 : 100, // 增加列宽
            className: isMobileView ? 'mobile-hidden' : '', // 移动端隐藏
            render: (minutes) => (
                <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
                    {`${minutes || 0} 分钟`}
                </span>
            )
        },
        {
            title: isMobileView ? '姓名' : '用户姓名',
            dataIndex: 'assignedUserFullName',
            key: 'assignedUserFullName',
            width: isMobileView ? 80 : 120,
            ellipsis: true,
            className: isMobileView ? 'mobile-hidden' : '', // 移动端隐藏
            render: (fullName) => (
                <span style={{ fontSize: isMobileView ? '11px' : '12px' }}>
                    {fullName || '-'}
                </span>
            )
        },
        {
            title: isMobileView ? '账号' : 'Zoom账号',
            dataIndex: 'zoomAuthAccountName',
            key: 'zoomAuthAccountName',
            width: isMobileView ? 80 : 120,
            ellipsis: true,
            render: (accountName) => (
                <Tooltip title={accountName}>
                    <Text style={{ fontSize: isMobileView ? '11px' : '12px' }}>
                        {accountName || '-'}
                    </Text>
                </Tooltip>
            )
        },
        {
            title: 'email',
            dataIndex: 'assignedZoomUserEmail',
            key: 'assignedZoomUserEmail',
            width: isMobileView ? 120 : 200, // 增加列宽
            className: isMobileView ? 'mobile-hidden' : '', // 移动端隐藏
            render: (email) => {
                if (!email || email === '-') {
                    return <span style={{ fontSize: isMobileView ? '11px' : '12px' }}>-</span>;
                }
                return (
                    <div style={{
                        fontSize: isMobileView ? '11px' : '12px',
                        wordBreak: 'break-all', // 支持换行
                        whiteSpace: 'normal'    // 允许换行
                    }}>
                        <a
                            href={`https://m.zoombus.com/zoom-users?email=${encodeURIComponent(email)}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{ color: '#1890ff' }}
                        >
                            {email}
                        </a>
                    </div>
                );
            }
        },

        {
            title: '操作',
            key: 'action',
            width: isMobileView ? 80 : 120,
            fixed: isMobileView ? false : 'right', // PC端固定操作栏
            render: (_, record) => (
                <Space size={isMobileView ? 'small' : 'middle'}>
                    {record.status === 'PENDING' ? (
                        <Tooltip title="取消会议">
                            <Button
                                type="primary"
                                danger
                                size={isMobileView ? 'small' : 'small'}
                                icon={<StopOutlined />}
                                loading={endingMeetings.has(record.id)}
                                disabled={endingMeetings.has(record.id)}
                                onClick={() => {
                                    Modal.confirm({
                                        title: '确认取消会议',
                                        content: (
                                            <div>
                                                <p>确定要取消这个待开启的会议吗？</p>
                                                <p style={{ color: '#666', fontSize: '12px' }}>
                                                    会议主题：{record.topic || '无主题'}
                                                </p>
                                                {record.zoomAuthAccountName && (
                                                    <p style={{ color: '#666', fontSize: '12px' }}>
                                                        Zoom账号：{record.zoomAuthAccountName}
                                                    </p>
                                                )}
                                            </div>
                                        ),
                                        onOk: () => endMeeting(record.id, 'PENDING'),
                                        okText: '确认取消',
                                        cancelText: '保留会议',
                                        okType: 'danger'
                                    });
                                }}
                            >
                                {endingMeetings.has(record.id) ? '取消中...' : '取消'}
                            </Button>
                        </Tooltip>
                    ) : (
                        <Tooltip title="结束会议">
                            <Button
                                type="primary"
                                danger
                                size="small"
                                icon={<StopOutlined />}
                                loading={endingMeetings.has(record.id)}
                                disabled={endingMeetings.has(record.id)}
                                onClick={() => {
                                    Modal.confirm({
                                        title: '确认结束会议',
                                        content: (
                                            <div>
                                                <p>确定要结束这个正在进行的会议吗？</p>
                                                <p style={{ color: '#666', fontSize: '12px' }}>
                                                    会议主题：{record.topic || '无主题'}
                                                </p>
                                                <p style={{ color: '#666', fontSize: '12px' }}>
                                                    已进行：{record.startTime ?
                                                        `${dayjs().diff(dayjs(record.startTime), 'minute')} 分钟` :
                                                        '未知'}
                                                </p>
                                                {record.zoomAuthAccountName && (
                                                    <p style={{ color: '#666', fontSize: '12px' }}>
                                                        Zoom账号：{record.zoomAuthAccountName}
                                                    </p>
                                                )}
                                            </div>
                                        ),
                                        onOk: () => endMeeting(record.id, 'USING'),
                                        okText: '确认结束',
                                        cancelText: '继续会议',
                                        okType: 'danger'
                                    });
                                }}
                            >
                                {endingMeetings.has(record.id) ? '结束中...' : '结束'}
                            </Button>
                        </Tooltip>
                    )}
                </Space>
            )
        }
    ];

    // 历史会议表格列
    const historyMeetingColumns = [
        {
            title: isMobileView ? '主题' : '会议主题',
            dataIndex: 'topic',
            key: 'topic',
            width: isMobileView ? 120 : 200,
            ellipsis: true,
            render: (text) => (
                <span style={{ fontSize: isMobileView ? '12px' : '14px' }}>
                    {text || '无主题'}
                </span>
            )
        },
        {
            title: isMobileView ? '会议号' : '会议号',
            dataIndex: 'zoomMeetingId',
            key: 'zoomMeetingId',
            width: isMobileView ? 100 : 120,
            render: (text) => (
                <span style={{
                    fontSize: isMobileView ? '11px' : '14px',
                    fontFamily: 'monospace'
                }}>
                    {text}
                </span>
            )
        },
        {
            title: '姓名',
            dataIndex: 'assignedUserFullName',
            key: 'assignedUserFullName',
            width: isMobileView ? 80 : 120,
            ellipsis: true,
            render: (text) => (
                <span style={{
                    fontSize: isMobileView ? '11px' : '14px'
                }}>
                    {text || '-'}
                </span>
            )
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: isMobileView ? 60 : 80,
            render: (status) => (
                <div style={{ fontSize: isMobileView ? '10px' : '12px' }}>
                    {getStatusTag(status)}
                </div>
            )
        },
        {
            title: '会议来源',
            dataIndex: 'creationSource',
            key: 'creationSource',
            width: 100,
            render: getCreationSourceTag
        },
        {
            title: '计费模式',
            dataIndex: 'billingMode',
            key: 'billingMode',
            width: 100,
            render: getBillingModeTag
        },
        {
            title: isMobileView ? '开始' : '开始时间',
            dataIndex: 'startTime',
            key: 'startTime',
            width: isMobileView ? 80 : 160,
            render: (time) => (
                <span style={{ fontSize: isMobileView ? '10px' : '14px' }}>
                    {time ? (isMobileView ?
                        dayjs(time).format('MM-DD HH:mm') :
                        dayjs(time).format('YYYY-MM-DD HH:mm:ss')
                    ) : '-'}
                </span>
            )
        },
        {
            title: '会议时长',
            dataIndex: 'durationMinutes',
            key: 'durationMinutes',
            width: 100,
            render: (minutes) => `${minutes || 0} 分钟`
        },
        {
            title: isMobileView ? '结算' : '是否结算',
            dataIndex: 'isSettled',
            key: 'isSettled',
            width: isMobileView ? 60 : 100, // 增加列宽防止换行
            render: (isSettled) => (
                <Tag color={isSettled ? 'green' : 'orange'} style={{ fontSize: isMobileView ? '10px' : '12px' }}>
                    {isSettled ? '已结算' : '未结算'}
                </Tag>
            )
        },
        {
            title: isMobileView ? '姓名' : '用户姓名',
            dataIndex: 'assignedUserFullName',
            key: 'assignedUserFullName',
            width: isMobileView ? 80 : 120,
            ellipsis: true,
            render: (fullName) => (
                <span style={{ fontSize: isMobileView ? '11px' : '12px' }}>
                    {fullName || '-'}
                </span>
            )
        },
        {
            title: 'email',
            dataIndex: 'assignedZoomUserEmail',
            key: 'assignedZoomUserEmail',
            width: isMobileView ? 120 : 200, // 增加列宽
            render: (email) => {
                if (!email || email === '-') {
                    return <span style={{ fontSize: isMobileView ? '11px' : '12px' }}>-</span>;
                }
                return (
                    <div style={{
                        fontSize: isMobileView ? '11px' : '12px',
                        wordBreak: 'break-all', // 支持换行
                        whiteSpace: 'normal'    // 允许换行
                    }}>
                        <a
                            href={`https://m.zoombus.com/zoom-users?email=${encodeURIComponent(email)}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{ color: '#1890ff' }}
                        >
                            {email}
                        </a>
                    </div>
                );
            }
        },
        {
            title: '结束时间',
            dataIndex: 'endTime',
            key: 'endTime',
            width: 160,
            render: (time) => time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-'
        },
        {
            title: '操作',
            key: 'action',
            width: isMobileView ? 80 : 120,
            fixed: isMobileView ? false : 'right',
            render: (_, record) => (
                <Space size={isMobileView ? 'small' : 'middle'}>
                    <Tooltip title="查看会议报告">
                        <Button
                            type="link"
                            size="small"
                            icon={<EyeOutlined />}
                            onClick={() => handleViewMeetingReport(record)}
                            style={{
                                fontSize: isMobileView ? '12px' : '14px',
                                padding: isMobileView ? '2px 4px' : '4px 8px'
                            }}
                        >
                            {isMobileView ? '' : '报告'}
                        </Button>
                    </Tooltip>
                </Space>
            )
        }
    ];

    // 手动刷新
    const handleManualRefresh = () => {
        fetchDashboardData();
        if (activeTab === 'active') {
            fetchActiveMeetings();
        } else {
            fetchHistoryMeetings();
        }
        message.success('数据已刷新');
    };

    // 切换自动刷新
    const handleAutoRefreshToggle = (checked) => {
        setAutoRefresh(checked);
        if (checked) {
            message.success('已开启自动刷新（每30秒）');
        } else {
            message.info('已关闭自动刷新');
        }
    };

    // 监听窗口大小变化
    useEffect(() => {
        const handleResize = () => {
            setIsMobileView(isMobile());
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // 自动刷新功能 - 每30秒刷新一次
    useEffect(() => {
        if (!autoRefresh || activeTab !== 'active') return;

        const interval = setInterval(() => {
            fetchDashboardData();
            fetchActiveMeetings();
        }, 30000); // 30秒 = 30000毫秒

        return () => clearInterval(interval);
    }, [autoRefresh, activeTab]);

    useEffect(() => {
        fetchDashboardData();
    }, []);

    useEffect(() => {
        if (activeTab === 'active') {
            fetchActiveMeetings();
        } else {
            fetchHistoryMeetings();
        }
    }, [activeTab, activeFilters, historyFilters]);

    return (
        <div className="zoom-meeting-dashboard" style={{ padding: isMobileView ? '12px' : '24px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                <h2 style={{ fontSize: isMobileView ? '18px' : '24px', margin: 0 }}>Zoom会议看板</h2>
                {lastRefreshTime && (
                    <span style={{ fontSize: '12px', color: '#666' }}>
                        最后更新: {lastRefreshTime.toLocaleTimeString()}
                    </span>
                )}
            </div>

            {/* 统计卡片 */}
            <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
                <Col xs={12} sm={12} md={6} lg={6} xl={6}>
                    <Card>
                        <Statistic
                            title="活跃会议"
                            value={dashboardData.activeMeetingsCount || 0}
                            prefix={<VideoCameraOutlined />}
                            valueStyle={{ color: '#3f8600' }}
                        />
                    </Card>
                </Col>
                <Col xs={12} sm={12} md={6} lg={6} xl={6}>
                    <Card>
                        <Statistic
                            title="今日会议"
                            value={dashboardData.todayMeetingsCount || 0}
                            prefix={<CalendarOutlined />}
                            valueStyle={{ color: '#1890ff' }}
                        />
                    </Card>
                </Col>
            </Row>

            {/* 会议列表 */}
            <Card>
                <Tabs activeKey={activeTab} onChange={setActiveTab}>
                    <TabPane tab="活跃会议" key="active">
                        {/* 活跃会议过滤器 */}
                        <div style={{ marginBottom: 16 }}>
                            <Space direction={isMobileView ? 'vertical' : 'horizontal'} style={{ width: '100%' }}>
                                <Input
                                    placeholder="搜索会议主题或会议号"
                                    prefix={<SearchOutlined />}
                                    value={activeFilters.keyword}
                                    onChange={(e) => setActiveFilters({
                                        ...activeFilters,
                                        keyword: e.target.value
                                    })}
                                    style={{ width: isMobileView ? '100%' : 200 }}
                                />
                                <Select
                                    placeholder="计费模式"
                                    value={activeFilters.billingMode}
                                    onChange={(value) => setActiveFilters({
                                        ...activeFilters,
                                        billingMode: value
                                    })}
                                    style={{ width: 120 }}
                                    allowClear
                                >
                                    <Option value="LONG">按时段</Option>
                                    <Option value="BY_TIME">按时长</Option>
                                </Select>
                                <Tooltip title="手动刷新数据">
                                    <Button
                                        icon={<SyncOutlined spin={loading} />}
                                        onClick={handleManualRefresh}
                                        loading={loading}
                                    >
                                        {!isMobileView && '刷新'}
                                    </Button>
                                </Tooltip>

                                <Tooltip title={autoRefresh ? '关闭自动刷新' : '开启自动刷新（每30秒）'}>
                                    <Space>
                                        <Switch
                                            checked={autoRefresh}
                                            onChange={handleAutoRefreshToggle}
                                            size="small"
                                        />
                                        {!isMobileView && (
                                            <span style={{ fontSize: '12px', color: '#666' }}>
                                                自动刷新
                                            </span>
                                        )}
                                    </Space>
                                </Tooltip>
                            </Space>
                        </div>
                        
                        {/* 移动端滚动提示 */}
                        {isMobileView && (
                            <div style={{
                                marginBottom: 16,
                                padding: '8px 12px',
                                backgroundColor: '#f0f2f5',
                                borderRadius: '4px',
                                fontSize: '12px',
                                color: '#666'
                            }}>
                                👈 表格可左右滑动查看所有列信息
                            </div>
                        )}

                        <div data-page="zoom-meeting-dashboard">
                          <Table
                              columns={activeMeetingColumns}
                              dataSource={activeMeetings}
                              rowKey="id"
                              loading={loading}
                              pagination={{
                                  ...pagination,
                                  onChange: fetchActiveMeetings,
                                  showSizeChanger: !isMobileView,
                                  showQuickJumper: !isMobileView,
                                  showTotal: (total, range) =>
                                      isMobileView
                                          ? `${range[0]}-${range[1]} / ${total}`
                                          : `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
                                  simple: isMobileView,
                                  size: isMobileView ? 'small' : 'default'
                              }}
                              scroll={{ x: isMobileView ? 400 : 'max-content' }}
                              size={isMobileView ? 'small' : 'middle'}
                          />
                        </div>
                    </TabPane>
                    
                    <TabPane tab="历史会议" key="history">
                        {/* 历史会议过滤器 */}
                        <div style={{ marginBottom: 16 }}>
                            <Space wrap>
                                <Input
                                    placeholder="搜索会议主题或会议号"
                                    prefix={<SearchOutlined />}
                                    value={historyFilters.keyword}
                                    onChange={(e) => setHistoryFilters({
                                        ...historyFilters,
                                        keyword: e.target.value
                                    })}
                                    style={{ width: 200 }}
                                />
                                <Select
                                    placeholder="计费模式"
                                    value={historyFilters.billingMode}
                                    onChange={(value) => setHistoryFilters({
                                        ...historyFilters,
                                        billingMode: value
                                    })}
                                    style={{ width: 120 }}
                                    allowClear
                                >
                                    <Option value="LONG">按时段</Option>
                                    <Option value="BY_TIME">按时长</Option>
                                </Select>
                                <RangePicker
                                    value={historyFilters.dateRange}
                                    onChange={(dates) => setHistoryFilters({
                                        ...historyFilters,
                                        dateRange: dates
                                    })}
                                    style={{ width: 240 }}
                                />
                                <Button 
                                    icon={<ReloadOutlined />}
                                    onClick={() => fetchHistoryMeetings()}
                                >
                                    刷新
                                </Button>
                            </Space>
                        </div>
                        
                        <div data-page="zoom-meeting-history">
                            <Table
                                columns={historyMeetingColumns}
                                dataSource={historyMeetings}
                                rowKey="id"
                                loading={loading}
                                size={isMobileView ? 'small' : 'middle'}
                                pagination={{
                                    ...pagination,
                                    onChange: fetchHistoryMeetings,
                                    showSizeChanger: !isMobileView,
                                    showQuickJumper: !isMobileView,
                                    showTotal: (total, range) =>
                                        isMobileView
                                            ? `${range[0]}-${range[1]} / ${total}`
                                            : `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
                                    simple: isMobileView,
                                    size: isMobileView ? 'small' : 'default'
                                }}
                                scroll={{ x: isMobileView ? 400 : 'max-content' }}
                            />
                        </div>
                    </TabPane>

                    <TabPane tab="统计分析" key="statistics">
                        <MeetingReportStatistics isMobileView={isMobileView} />
                    </TabPane>
                </Tabs>
            </Card>

            {/* 会议报告详情弹窗 */}
            <MeetingReportModal
                visible={reportModalVisible}
                onCancel={() => {
                    setReportModalVisible(false);
                    setCurrentMeeting(null);
                }}
                meeting={currentMeeting}
                isMobileView={isMobileView}
            />
        </div>
    );
};

export default ZoomMeetingDashboard;
