import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  DatePicker,
  Select,
  Input,
  Tag,
  Statistic,
  Row,
  Col,
  Modal,
  Typography,
  message,
  Tooltip,
  Progress,
  Divider,
  Tabs,
  Switch
} from 'antd';
import {
  ReloadOutlined,
  SearchOutlined,
  DownloadOutlined,
  DeleteOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  CopyOutlined,
  SyncOutlined
} from '@ant-design/icons';
import JsonView from '@uiw/react-json-view';
import api from '../services/api';
import moment from 'moment';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

// JSON解析辅助函数
const parseJsonSafely = (jsonString) => {
  if (!jsonString) return null;
  try {
    return JSON.parse(jsonString);
  } catch (e) {
    return null;
  }
};

// 复制到剪贴板
const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text).then(() => {
    message.success('已复制到剪贴板');
  }).catch(() => {
    message.error('复制失败');
  });
};

// JSON展示组件
const JsonDisplay = ({ title, content, isMobile }) => {
  if (!content) return null;

  const jsonData = parseJsonSafely(content);
  const isValidJson = jsonData !== null;

  return (
    <div style={{ marginBottom: '16px' }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '8px'
      }}>
        <Text strong>{title}:</Text>
        <Button
          type="text"
          size="small"
          icon={<CopyOutlined />}
          onClick={() => copyToClipboard(content)}
        >
          复制
        </Button>
      </div>

      {isValidJson ? (
        <div style={{
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          overflow: 'hidden'
        }}>
          <JsonView
            value={jsonData}
            style={{
              backgroundColor: '#fafafa',
              fontSize: isMobile ? '11px' : '12px',
              maxHeight: '300px',
              overflow: 'auto',
              padding: '12px'
            }}
            displayDataTypes={false}
            displayObjectSize={false}
            enableClipboard={false}
            collapsed={2}
          />
        </div>
      ) : (
        <pre style={{
          background: '#fafafa',
          padding: '12px',
          borderRadius: '6px',
          fontSize: isMobile ? '11px' : '12px',
          maxHeight: '300px',
          overflow: 'auto',
          border: '1px solid #d9d9d9',
          margin: 0
        }}>
          {content}
        </pre>
      )}
    </div>
  );
};

const ZoomApiLogs = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({});
  const [statsLoading, setStatsLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  
  // 查询条件
  const [filters, setFilters] = useState({
    businessType: null,
    isSuccess: null,
    apiPath: '',
    zoomUserId: '',
    timeRange: [moment().subtract(24, 'hours'), moment()],
    minDuration: null,
    maxDuration: null
  });
  
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedLog, setSelectedLog] = useState(null);
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState(null);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 加载数据
  useEffect(() => {
    loadLogs();
    loadStats();
  }, [pagination.current, pagination.pageSize, filters]);

  // 自动刷新功能 - 每30秒刷新一次
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadLogs();
      loadStats();
    }, 30000); // 30秒 = 30000毫秒

    return () => clearInterval(interval);
  }, [autoRefresh, pagination.current, pagination.pageSize, filters]);

  const loadLogs = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.current - 1,
        size: pagination.pageSize,
        businessType: filters.businessType,
        isSuccess: filters.isSuccess,
        apiPath: filters.apiPath || null,
        zoomUserId: filters.zoomUserId || null,
        startTime: filters.timeRange[0]?.format('YYYY-MM-DDTHH:mm:ss'),
        endTime: filters.timeRange[1]?.format('YYYY-MM-DDTHH:mm:ss'),
        minDuration: filters.minDuration,
        maxDuration: filters.maxDuration,
        sortBy: 'requestTime',
        sortDir: 'desc'
      };

      const response = await api.get('/admin/zoom-api-logs', { params });
      if (response.data.success) {
        setLogs(response.data.data.content || []);
        setPagination(prev => ({
          ...prev,
          total: response.data.data.totalElements || 0
        }));
        // 更新最后刷新时间
        setLastRefreshTime(new Date());
      } else {
        message.error(response.data.message || '加载日志失败');
      }
    } catch (error) {
      console.error('加载日志失败:', error);
      message.error('加载日志失败');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      setStatsLoading(true);
      const params = {
        startTime: filters.timeRange[0]?.format('YYYY-MM-DDTHH:mm:ss'),
        endTime: filters.timeRange[1]?.format('YYYY-MM-DDTHH:mm:ss')
      };

      const response = await api.get('/admin/zoom-api-logs/stats', { params });
      if (response.data.success) {
        setStats(response.data.data || {});
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    loadLogs();
    loadStats();
  };

  const handleReset = () => {
    setFilters({
      businessType: null,
      isSuccess: null,
      apiPath: '',
      zoomUserId: '',
      timeRange: [moment().subtract(24, 'hours'), moment()],
      minDuration: null,
      maxDuration: null
    });
  };

  // 手动刷新
  const handleManualRefresh = () => {
    loadLogs();
    loadStats();
    message.success('数据已刷新');
  };

  // 切换自动刷新
  const handleAutoRefreshToggle = (checked) => {
    setAutoRefresh(checked);
    if (checked) {
      message.success('已开启自动刷新（每30秒）');
    } else {
      message.info('已关闭自动刷新');
    }
  };

  const handleExport = async () => {
    try {
      const params = {
        businessType: filters.businessType,
        isSuccess: filters.isSuccess,
        startTime: filters.timeRange[0]?.format('YYYY-MM-DDTHH:mm:ss'),
        endTime: filters.timeRange[1]?.format('YYYY-MM-DDTHH:mm:ss'),
        limit: 1000
      };

      const response = await api.get('/admin/zoom-api-logs/export', { 
        params,
        responseType: 'blob'
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `zoom-api-logs-${moment().format('YYYY-MM-DD-HH-mm')}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    }
  };

  const handleCleanup = () => {
    Modal.confirm({
      title: '确认清理',
      content: '确定要清理30天前的API日志吗？此操作不可恢复。',
      onOk: async () => {
        try {
          const response = await api.delete('/admin/zoom-api-logs/cleanup?daysToKeep=30');
          if (response.data.success) {
            message.success('清理完成');
            loadLogs();
            loadStats();
          } else {
            message.error(response.data.message || '清理失败');
          }
        } catch (error) {
          console.error('清理失败:', error);
          message.error('清理失败');
        }
      }
    });
  };

  const showLogDetail = (record) => {
    setSelectedLog(record);
    setDetailModalVisible(true);
  };

  const formatDuration = (ms) => {
    if (!ms) return '-';
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
    return `${(ms / 60000).toFixed(2)}min`;
  };

  const getStatusColor = (isSuccess, responseStatus) => {
    if (isSuccess) return 'success';
    if (responseStatus >= 400 && responseStatus < 500) return 'warning';
    if (responseStatus >= 500) return 'error';
    return 'default';
  };

  const getDurationColor = (ms) => {
    if (!ms) return 'default';
    if (ms > 30000) return 'error';
    if (ms > 5000) return 'warning';
    return 'success';
  };

  const columns = [
    {
      title: '请求时间',
      dataIndex: 'requestTime',
      key: 'requestTime',
      width: isMobileView ? 120 : 160,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {moment(text).format(isMobileView ? 'MM-DD HH:mm' : 'YYYY-MM-DD HH:mm:ss')}
        </span>
      ),
    },
    {
      title: 'API路径',
      dataIndex: 'apiPath',
      key: 'apiPath',
      width: isMobileView ? 150 : 200,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <Text code style={{ fontSize: isMobileView ? '10px' : '12px' }}>{text}</Text>
        </Tooltip>
      ),
    },
    {
      title: '方法',
      dataIndex: 'apiMethod',
      key: 'apiMethod',
      width: isMobileView ? 50 : 70,
      render: (text) => (
        <Tag color={text === 'GET' ? 'blue' : text === 'POST' ? 'green' : 'orange'} 
             style={{ fontSize: isMobileView ? '10px' : '12px' }}>
          {text}
        </Tag>
      ),
    },
    {
      title: isMobileView ? '类型' : '业务类型',
      dataIndex: 'businessType',
      key: 'businessType',
      width: isMobileView ? 80 : 120,
      render: (text) => (
        <Tag style={{ fontSize: isMobileView ? '10px' : '12px' }}>{text}</Tag>
      ),
    },
    {
      title: isMobileView ? '账号' : '调用账号',
      key: 'zoomUserAccount',
      width: isMobileView ? 120 : 180,
      ellipsis: true,
      render: (_, record) => {
        const email = record.zoomUserEmail;
        const userId = record.zoomUserId;

        if (!email && !userId) {
          return <Text type="secondary" style={{ fontSize: isMobileView ? '10px' : '12px' }}>-</Text>;
        }

        const displayText = isMobileView ? (email ? email.split('@')[0] : `ID: ${userId}`) : (email || `ID: ${userId}`);

        if (userId) {
          return (
            <Tooltip title={email || `ID: ${userId}`}>
              <a
                href={`http://localhost:3000/zoom-users/${userId}`}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: '#1890ff',
                  textDecoration: 'none',
                  fontSize: isMobileView ? '10px' : '12px'
                }}
              >
                {displayText}
              </a>
            </Tooltip>
          );
        }

        return (
          <Tooltip title={email}>
            <Text style={{ fontSize: isMobileView ? '10px' : '12px' }}>
              {displayText}
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'isSuccess',
      key: 'isSuccess',
      width: isMobileView ? 60 : 80,
      render: (isSuccess, record) => (
        <Tag 
          color={getStatusColor(isSuccess, record.responseStatus)}
          icon={isSuccess ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
          style={{ fontSize: isMobileView ? '10px' : '12px' }}
        >
          {isSuccess ? '成功' : '失败'}
        </Tag>
      ),
    },
    {
      title: '耗时',
      dataIndex: 'durationMs',
      key: 'durationMs',
      width: isMobileView ? 70 : 90,
      render: (ms) => (
        <Tag color={getDurationColor(ms)} style={{ fontSize: isMobileView ? '10px' : '12px' }}>
          {formatDuration(ms)}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 60 : 80,
      render: (_, record) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          size="small"
          onClick={() => showLogDetail(record)}
          style={{ fontSize: isMobileView ? '11px' : '14px' }}
        >
          {isMobileView ? '' : '详情'}
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: isMobileView ? '12px' : '24px' }}>
      <Title level={2} style={{ marginBottom: '24px' }}>
        Zoom API调用日志
      </Title>

      {/* 统计卡片 */}
      {stats.overall && (
        <div>
          {lastRefreshTime && (
            <div style={{
              textAlign: 'right',
              marginBottom: '8px',
              fontSize: '12px',
              color: '#666'
            }}>
              最后更新: {moment(lastRefreshTime).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          )}
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="总请求数"
                value={stats.overall.totalRequests}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="成功率"
                value={stats.overall.successRate}
                precision={1}
                suffix="%"
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: stats.overall.successRate > 95 ? '#3f8600' : '#cf1322' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="平均耗时"
                value={stats.overall.avgDuration}
                precision={0}
                suffix="ms"
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="慢请求"
                value={stats.overall.slowRequests}
                prefix={<CloseCircleOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>
        </div>
      )}

      {/* 查询条件 */}
      <Card style={{ marginBottom: '16px' }}>
        <Space direction={isMobileView ? 'vertical' : 'horizontal'} 
               style={{ width: '100%' }} 
               wrap>
          <RangePicker
            value={filters.timeRange}
            onChange={(dates) => setFilters(prev => ({ ...prev, timeRange: dates }))}
            showTime
            format="YYYY-MM-DD HH:mm"
            style={{ width: isMobileView ? '100%' : 300 }}
          />
          
          <Select
            placeholder="业务类型"
            value={filters.businessType}
            onChange={(value) => setFilters(prev => ({ ...prev, businessType: value }))}
            allowClear
            style={{ width: isMobileView ? '100%' : 150 }}
          >
            <Option value="USER_INFO">用户信息</Option>
            <Option value="UPDATE_PMI">更新PMI</Option>
            <Option value="CREATE_MEETING">创建会议</Option>
            <Option value="GET_MEETING">获取会议</Option>
            <Option value="CREATE_USER">创建用户</Option>
          </Select>
          
          <Select
            placeholder="成功状态"
            value={filters.isSuccess}
            onChange={(value) => setFilters(prev => ({ ...prev, isSuccess: value }))}
            allowClear
            style={{ width: isMobileView ? '100%' : 120 }}
          >
            <Option value={true}>成功</Option>
            <Option value={false}>失败</Option>
          </Select>
          
          <Input
            placeholder="API路径"
            value={filters.apiPath}
            onChange={(e) => setFilters(prev => ({ ...prev, apiPath: e.target.value }))}
            style={{ width: isMobileView ? '100%' : 200 }}
          />
          
          <Space>
            <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
              查询
            </Button>
            <Button onClick={handleReset}>重置</Button>
            <Tooltip title="手动刷新数据">
              <Button
                icon={<SyncOutlined spin={loading} />}
                onClick={handleManualRefresh}
                loading={loading}
              >
                {!isMobileView && '刷新'}
              </Button>
            </Tooltip>
            <Tooltip title={autoRefresh ? '关闭自动刷新' : '开启自动刷新（每30秒）'}>
              <Space>
                <Switch
                  checked={autoRefresh}
                  onChange={handleAutoRefreshToggle}
                  size="small"
                />
                {!isMobileView && (
                  <span style={{ fontSize: '12px', color: '#666' }}>
                    自动刷新
                  </span>
                )}
              </Space>
            </Tooltip>
            <Button icon={<DownloadOutlined />} onClick={handleExport}>
              导出
            </Button>
            <Button icon={<DeleteOutlined />} danger onClick={handleCleanup}>
              清理
            </Button>
          </Space>
        </Space>
      </Card>

      {/* 日志表格 */}
      <Card>
        <div data-page="zoom-api-logs">
          <Table
            columns={columns}
            dataSource={logs}
            rowKey="id"
            loading={loading}
            size={isMobileView ? 'small' : 'middle'}
            scroll={{ x: isMobileView ? 800 : 'auto' }}
            pagination={{
              ...pagination,
              showSizeChanger: !isMobileView,
              showQuickJumper: !isMobileView,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              simple: isMobileView,
              size: isMobileView ? 'small' : 'default',
              onChange: (page, pageSize) => {
                setPagination(prev => ({ ...prev, current: page, pageSize }));
              },
            }}
          />
        </div>
      </Card>

      {/* 详情弹窗 */}
      <Modal
        title="API调用详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={isMobileView ? '95%' : 1000}
        style={{ top: 20 }}
      >
        {selectedLog && (
          <div>
            {/* 基本信息 */}
            <Card size="small" style={{ marginBottom: '16px' }}>
              <Row gutter={[16, 8]}>
                <Col xs={24} sm={12} md={8}>
                  <Text strong>请求ID:</Text><br />
                  <Text code style={{ fontSize: isMobileView ? '10px' : '12px' }}>
                    {selectedLog.requestId}
                  </Text>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Text strong>请求时间:</Text><br />
                  <Text style={{ fontSize: isMobileView ? '11px' : '14px' }}>
                    {moment(selectedLog.requestTime).format('YYYY-MM-DD HH:mm:ss')}
                  </Text>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Text strong>响应时间:</Text><br />
                  <Text style={{ fontSize: isMobileView ? '11px' : '14px' }}>
                    {selectedLog.responseTime ?
                      moment(selectedLog.responseTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
                  </Text>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Text strong>API路径:</Text><br />
                  <Text code style={{ fontSize: isMobileView ? '10px' : '12px' }}>
                    {selectedLog.apiPath}
                  </Text>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Text strong>HTTP方法:</Text><br />
                  <Tag color="blue">{selectedLog.apiMethod}</Tag>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Text strong>业务类型:</Text><br />
                  <Tag>{selectedLog.businessType}</Tag>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Text strong>响应状态:</Text><br />
                  <Tag color={getStatusColor(selectedLog.isSuccess, selectedLog.responseStatus)}>
                    {selectedLog.responseStatus || '-'}
                  </Tag>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Text strong>耗时:</Text><br />
                  <Tag color={getDurationColor(selectedLog.durationMs)}>
                    {formatDuration(selectedLog.durationMs)}
                  </Tag>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Text strong>业务ID:</Text><br />
                  <Text>{selectedLog.businessId || '-'}</Text>
                </Col>
              </Row>
            </Card>

            {/* 详细内容 - 使用标签页 */}
            <Tabs defaultActiveKey="response" size={isMobileView ? 'small' : 'default'}>
              {selectedLog.responseBody && (
                <TabPane tab="响应体" key="response">
                  <JsonDisplay
                    title="响应内容"
                    content={selectedLog.responseBody}
                    isMobile={isMobileView}
                  />
                </TabPane>
              )}

              {selectedLog.requestBody && (
                <TabPane tab="请求体" key="request">
                  <JsonDisplay
                    title="请求内容"
                    content={selectedLog.requestBody}
                    isMobile={isMobileView}
                  />
                </TabPane>
              )}

              {(selectedLog.requestHeaders || selectedLog.responseHeaders) && (
                <TabPane tab="请求头" key="headers">
                  {selectedLog.requestHeaders && (
                    <JsonDisplay
                      title="请求头"
                      content={selectedLog.requestHeaders}
                      isMobile={isMobileView}
                    />
                  )}
                  {selectedLog.responseHeaders && (
                    <JsonDisplay
                      title="响应头"
                      content={selectedLog.responseHeaders}
                      isMobile={isMobileView}
                    />
                  )}
                </TabPane>
              )}

              {selectedLog.errorMessage && (
                <TabPane tab="错误信息" key="error">
                  <div style={{ marginBottom: '16px' }}>
                    <Text strong style={{ color: '#ff4d4f' }}>错误代码:</Text><br />
                    <Tag color="error">{selectedLog.errorCode || 'UNKNOWN'}</Tag>
                  </div>
                  <div>
                    <Text strong style={{ color: '#ff4d4f' }}>错误信息:</Text>
                    <pre style={{
                      background: '#fff2f0',
                      padding: '12px',
                      borderRadius: '6px',
                      fontSize: isMobileView ? '11px' : '12px',
                      color: '#ff4d4f',
                      border: '1px solid #ffccc7',
                      margin: '8px 0 0 0'
                    }}>
                      {selectedLog.errorMessage}
                    </pre>
                  </div>
                </TabPane>
              )}

              <TabPane tab="完整URL" key="url">
                <div>
                  <Text strong>完整API URL:</Text>
                  <div style={{
                    background: '#f5f5f5',
                    padding: '12px',
                    borderRadius: '6px',
                    fontSize: isMobileView ? '11px' : '12px',
                    border: '1px solid #d9d9d9',
                    marginTop: '8px',
                    wordBreak: 'break-all'
                  }}>
                    {selectedLog.apiUrl}
                  </div>
                  <Button
                    type="text"
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={() => copyToClipboard(selectedLog.apiUrl)}
                    style={{ marginTop: '8px' }}
                  >
                    复制URL
                  </Button>
                </div>
              </TabPane>
            </Tabs>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ZoomApiLogs;
