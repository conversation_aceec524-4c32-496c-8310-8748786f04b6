import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Button,
  Tag,
  Space,
  Descriptions,
  message,
  Modal,
  Form,
  Input,
  Spin,
  Alert,
  Typography,
  Divider,
  Tooltip
} from 'antd';
import {
  ReloadOutlined,
  InfoCircleOutlined,
  HistoryOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CodeOutlined,
  DatabaseOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import api from '../services/api';

const { Title, Text } = Typography;
const { TextArea } = Input;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const VersionManagement = () => {
  const [loading, setLoading] = useState(false);
  const [currentVersion, setCurrentVersion] = useState(null);
  const [versionHistory, setVersionHistory] = useState([]);
  const [recordModalVisible, setRecordModalVisible] = useState(false);
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [form] = Form.useForm();

  // 获取当前版本信息
  const fetchCurrentVersion = async () => {
    try {
      setLoading(true);
      const response = await api.get('/version/current');
      setCurrentVersion(response.data);
    } catch (error) {
      message.error('获取当前版本信息失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 获取版本历史
  const fetchVersionHistory = async () => {
    try {
      const response = await api.get('/version/history?limit=20');
      if (response.data.success) {
        setVersionHistory(response.data.data);
      }
    } catch (error) {
      message.error('获取版本历史失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 手动记录版本信息
  const handleRecordVersion = async (values) => {
    try {
      setLoading(true);
      const response = await api.post('/version/record', values);
      if (response.data.success) {
        message.success('版本信息记录成功');
        setRecordModalVisible(false);
        form.resetFields();
        await fetchCurrentVersion();
        await fetchVersionHistory();
      } else {
        message.error(response.data.message || '记录版本信息失败');
      }
    } catch (error) {
      message.error('记录版本信息失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 刷新所有数据
  const handleRefresh = async () => {
    await Promise.all([fetchCurrentVersion(), fetchVersionHistory()]);
  };

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    handleRefresh();
  }, []);

  // 版本历史表格列定义
  const historyColumns = [
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: isMobileView ? 120 : 180,
      render: (text) => (
        <Space>
          <ClockCircleOutlined />
          <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
            {isMobileView ? text.substring(5, 16) : text}
          </span>
        </Space>
      ),
    },
    {
      title: isMobileView ? '应用' : '应用版本',
      dataIndex: 'application_version',
      key: 'application_version',
      width: isMobileView ? 80 : 120,
      render: (text) => (
        <Tag
          color="blue"
          icon={!isMobileView && <CodeOutlined />}
          style={{ fontSize: isMobileView ? '10px' : '12px' }}
        >
          {text}
        </Tag>
      ),
    },
    // 移动端隐藏数据库版本列
    ...(isMobileView ? [] : [{
      title: '数据库版本',
      dataIndex: 'database_version',
      key: 'database_version',
      width: 140,
      render: (text) => (
        <Tag color="green" icon={<DatabaseOutlined />}>
          {text}
        </Tag>
      ),
    }]),
    {
      title: isMobileView ? '事件' : '事件类型',
      dataIndex: 'event_type',
      key: 'event_type',
      width: isMobileView ? 80 : 140,
      render: (text) => {
        const colorMap = {
          'APPLICATION_STARTUP': 'blue',
          'DATABASE_MIGRATION': 'orange',
          'MANUAL_RECORD': 'purple',
          'DATABASE_INIT': 'green'
        };
        const labelMap = {
          'APPLICATION_STARTUP': isMobileView ? '启动' : '应用启动',
          'DATABASE_MIGRATION': isMobileView ? '迁移' : '数据库迁移',
          'MANUAL_RECORD': isMobileView ? '手动' : '手动记录',
          'DATABASE_INIT': isMobileView ? '初始' : '数据库初始化'
        };
        return (
          <Tag
            color={colorMap[text] || 'default'}
            style={{ fontSize: isMobileView ? '10px' : '12px' }}
          >
            {labelMap[text] || text}
          </Tag>
        );
      },
    },
    // 移动端隐藏服务器列
    ...(isMobileView ? [] : [{
      title: '服务器',
      dataIndex: 'server_info',
      key: 'server_info',
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    }]),
    // 移动端隐藏描述列
    ...(isMobileView ? [] : [{
      title: '描述',
      dataIndex: 'event_description',
      key: 'event_description',
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text || '-'}
        </Tooltip>
      ),
    }]),
  ];

  // 获取版本状态标签
  const getVersionStatus = () => {
    if (!currentVersion) return null;
    
    const { applicationVersion, databaseVersion } = currentVersion;
    
    // 简单的版本兼容性检查
    if (applicationVersion === '1.0.0' && databaseVersion.startsWith('20250804')) {
      return <Tag color="success" icon={<CheckCircleOutlined />}>版本兼容</Tag>;
    } else if (applicationVersion === '1.0.0') {
      return <Tag color="warning" icon={<ExclamationCircleOutlined />}>需要检查兼容性</Tag>;
    } else {
      return <Tag color="default" icon={<InfoCircleOutlined />}>未知状态</Tag>;
    }
  };

  return (
    <div style={{ padding: isMobileView ? '12px' : '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={isMobileView ? 3 : 2}>
          <CodeOutlined /> 版本管理
        </Title>
        <Text type="secondary">
          查看和管理应用程序版本与数据库版本的对应关系
        </Text>
      </div>

      {/* 当前版本信息卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={16}>
          <Card
            title={
              <Space>
                <InfoCircleOutlined />
                当前版本信息
              </Space>
            }
            extra={
              <Space>
                {getVersionStatus()}
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
              </Space>
            }
            loading={loading}
          >
            {currentVersion ? (
              <Descriptions column={1} bordered size="small">
                <Descriptions.Item label="应用名称">
                  <Tag color="blue">{currentVersion.applicationName}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="应用版本">
                  <Tag color="blue" icon={<CodeOutlined />}>
                    {currentVersion.applicationVersion}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="数据库版本">
                  <Tag color="green" icon={<DatabaseOutlined />}>
                    {currentVersion.databaseVersion}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="服务器信息">
                  <Text code>{currentVersion.serverInfo}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="查询时间">
                  <Text type="secondary">{currentVersion.timestamp}</Text>
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <Alert
                message="无法获取版本信息"
                description="请检查服务器连接状态"
                type="warning"
                showIcon
              />
            )}
          </Card>
        </Col>
        
        <Col xs={24} lg={8}>
          <Card
            title={
              <Space>
                <HistoryOutlined />
                快速操作
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type="primary"
                block
                icon={<HistoryOutlined />}
                onClick={() => setRecordModalVisible(true)}
              >
                手动记录版本
              </Button>
              <Button
                block
                icon={<ReloadOutlined />}
                onClick={fetchVersionHistory}
              >
                刷新历史记录
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 版本历史记录 */}
      <Card
        title={
          <Space>
            <HistoryOutlined />
            版本历史记录
          </Space>
        }
        extra={
          <Text type="secondary">
            显示最近20条记录
          </Text>
        }
      >
        <div data-page="version-management">
          <Table
            columns={historyColumns}
            dataSource={versionHistory}
            rowKey="id"
            size={isMobileView ? 'small' : 'middle'}
            pagination={{
              pageSize: 10,
              showSizeChanger: !isMobileView,
              showQuickJumper: !isMobileView,
              showTotal: (total, range) =>
                isMobileView
                  ? `${range[0]}-${range[1]} / ${total}`
                  : `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              simple: isMobileView,
              size: isMobileView ? 'small' : 'default'
            }}
            scroll={{ x: isMobileView ? 400 : 800 }}
          />
        </div>
      </Card>

      {/* 手动记录版本模态框 */}
      <Modal
        title="手动记录版本信息"
        open={recordModalVisible}
        onCancel={() => {
          setRecordModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleRecordVersion}
        >
          <Form.Item
            label="应用版本"
            name="applicationVersion"
            tooltip="留空则使用当前应用版本"
          >
            <Input placeholder="如：1.0.0（留空使用当前版本）" />
          </Form.Item>
          
          <Form.Item
            label="数据库版本"
            name="databaseVersion"
            tooltip="留空则使用当前数据库版本"
          >
            <Input placeholder="如：20250804_001（留空使用当前版本）" />
          </Form.Item>
          
          <Form.Item
            label="描述"
            name="description"
            rules={[{ required: true, message: '请输入描述信息' }]}
          >
            <TextArea
              rows={4}
              placeholder="请描述此次版本记录的原因，如：手动部署到生产环境、修复版本不一致问题等"
            />
          </Form.Item>
          
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setRecordModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                记录版本
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default VersionManagement;
