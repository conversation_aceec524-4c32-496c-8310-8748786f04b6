import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Input,
  message,
  Progress,
  Badge
} from 'antd';
import {
  ReloadOutlined,
  ClockCircleOutlined,
  InfoCircleOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  StopOutlined,
  SearchOutlined,
  ClearOutlined
} from '@ant-design/icons';
import { joinAccountWindowApi } from '../services/api';
import moment from 'moment';

const { Option } = Select;
const { RangePicker } = DatePicker;

const JoinAccountWindowQuery = () => {
  const [windows, setWindows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [filters, setFilters] = useState({});
  const [isMobileView, setIsMobileView] = useState(false);

  // 检测是否为移动端
  const isMobile = () => {
    return window.innerWidth <= 768;
  };

  // 获取使用窗口列表
  const fetchWindows = async (params = {}) => {
    setLoading(true);
    try {
      const queryParams = {
        page: pagination.current - 1,
        size: pagination.pageSize,
        ...filters,
        ...params,
      };
      
      const response = await joinAccountWindowApi.getWindows(queryParams);
      if (response.data.success) {
        const data = response.data.data;
        setWindows(data.content || []);
        setPagination(prev => ({
          ...prev,
          total: data.totalElements || 0,
        }));
      }
    } catch (error) {
      message.error('获取使用窗口列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const response = await joinAccountWindowApi.getStatistics();
      if (response.data.success) {
        setStatistics(response.data.data);
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  };

  useEffect(() => {
    fetchWindows();
    fetchStatistics();
  }, [pagination.current, pagination.pageSize, filters]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    // 初始检测
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 开启窗口
  const handleOpenWindow = async (id) => {
    try {
      await joinAccountWindowApi.openWindow(id);
      message.success('窗口开启成功');
      fetchWindows();
      fetchStatistics();
    } catch (error) {
      message.error('窗口开启失败');
    }
  };

  // 关闭窗口
  const handleCloseWindow = async (id) => {
    try {
      await joinAccountWindowApi.closeWindow(id);
      message.success('窗口关闭成功');
      fetchWindows();
      fetchStatistics();
    } catch (error) {
      message.error('窗口关闭失败');
    }
  };

  // 清除窗口错误信息
  const handleClearError = async (id) => {
    try {
      await joinAccountWindowApi.clearWindowError(id);
      message.success('错误信息已清除');
      fetchWindows();
    } catch (error) {
      message.error('清除错误信息失败');
    }
  };

  // 状态标签颜色
  const getStatusColor = (status) => {
    const colors = {
      PENDING: 'orange',
      ACTIVE: 'green',
      CLOSED: 'default'
    };
    return colors[status] || 'default';
  };

  // 状态描述
  const getStatusText = (status) => {
    const texts = {
      PENDING: '待开启',
      ACTIVE: '使用中',
      CLOSED: '已关闭'
    };
    return texts[status] || status;
  };

  // 计算窗口进度
  const calculateProgress = (window) => {
    const now = moment();
    const start = moment(window.startTime);
    const end = moment(window.endTime);
    
    if (now.isBefore(start)) {
      return { percent: 0, status: 'normal' };
    } else if (now.isAfter(end)) {
      return { percent: 100, status: 'success' };
    } else {
      const total = end.diff(start);
      const elapsed = now.diff(start);
      const percent = Math.round((elapsed / total) * 100);
      return { percent, status: 'active' };
    }
  };

  // 获取窗口状态描述
  const getWindowStatusDescription = (window) => {
    const now = moment();
    const start = moment(window.startTime);
    const end = moment(window.endTime);
    
    if (window.status === 'PENDING') {
      if (now.isBefore(start)) {
        const minutesToStart = start.diff(now, 'minutes');
        return `等待开启（${minutesToStart}分钟后开始）`;
      } else {
        return '待开启（已到时间）';
      }
    } else if (window.status === 'ACTIVE') {
      if (now.isAfter(end)) {
        return '使用中（已超时）';
      } else {
        const minutesToEnd = end.diff(now, 'minutes');
        return `使用中（${minutesToEnd}分钟后结束）`;
      }
    } else {
      return '已关闭';
    }
  };

  const columns = [
    {
      title: 'Token编号',
      dataIndex: 'tokenNumber',
      key: 'tokenNumber',
      width: isMobileView ? 100 : 120,
      render: (text) => (
        <a
          href={`http://localhost:3000/join-account/tokens?tokenNumber=${text}`}
          target="_blank"
          rel="noopener noreferrer"
          style={{
            fontFamily: 'monospace',
            color: '#1890ff',
            textDecoration: 'none',
            fontSize: isMobileView ? '12px' : '14px'
          }}
        >
          {text}
        </a>
      ),
    },
    {
      title: 'Zoom账号',
      key: 'zoomAccount',
      width: isMobileView ? 140 : 180,
      render: (_, record) => {
        const zoomUserId = record.zoomUserId;
        const zoomUserEmail = record.zoomUserEmail;
        if (zoomUserId) {
          return (
            <div style={{ fontSize: isMobileView ? '12px' : '14px' }}>
              <a
                href={`http://localhost:3000/zoom-users/${zoomUserId}`}
                target="_blank"
                rel="noopener noreferrer"
                style={{ color: '#1890ff', textDecoration: 'none' }}
              >
                {zoomUserEmail || `ID: ${zoomUserId}`}
              </a>
              {zoomUserEmail && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  ID: {zoomUserId}
                </div>
              )}
            </div>
          );
        }
        return '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status, record) => (
        <Tooltip title={getWindowStatusDescription(record)}>
          <Tag color={getStatusColor(status)}>
            {getStatusText(status)}
          </Tag>
        </Tooltip>
      ),
    },
    {
      title: '使用窗口',
      key: 'timeWindow',
      width: 300,
      render: (_, record) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <strong>开始:</strong> {moment(record.startTime).format('YYYY-MM-DD HH:mm:ss')}
          </div>
          <div style={{ marginBottom: 4 }}>
            <strong>结束:</strong> {moment(record.endTime).format('YYYY-MM-DD HH:mm:ss')}
          </div>
          <div>
            <strong>进度:</strong>
            <Progress
              {...calculateProgress(record)}
              size="small"
              style={{ marginLeft: 8, width: 100 }}
            />
          </div>
        </div>
      ),
    },
    {
      title: '实际使用',
      key: 'actualUsage',
      width: 200,
      render: (_, record) => {
        if (record.openedAt) {
          const openTime = moment(record.openedAt).format('HH:mm:ss');
          const closeTime = record.closedAt ? moment(record.closedAt).format('HH:mm:ss') : '进行中';
          const duration = record.closedAt ? 
            moment(record.closedAt).diff(moment(record.openedAt), 'minutes') :
            moment().diff(moment(record.openedAt), 'minutes');
          
          return (
            <div>
              <div>{openTime} - {closeTime}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                时长: {duration}分钟
              </div>
            </div>
          );
        }
        return '-';
      },
    },
    {
      title: '错误信息',
      key: 'errorInfo',
      width: 200,
      render: (_, record) => {
        // 调试信息：打印BTBJ2A的数据
        if (record.tokenNumber === 'BTBJ2A') {
          console.log('BTBJ2A 错误信息调试:', {
            tokenNumber: record.tokenNumber,
            errorMessage: record.errorMessage,
            lastOperationError: record.lastOperationError,
            errorMessageType: typeof record.errorMessage,
            lastOperationErrorType: typeof record.lastOperationError
          });
        }

        // 确保字段存在且不为空字符串
        const hasLastError = record.lastOperationError && record.lastOperationError.trim() !== '';
        const hasHistoryError = record.errorMessage && record.errorMessage.trim() !== '';

        if (hasLastError || hasHistoryError) {
          return (
            <div>
              {hasLastError && (
                <Tooltip title={record.lastOperationError}>
                  <Tag color="red" style={{ marginBottom: 4, cursor: 'pointer' }}>
                    最新错误
                  </Tag>
                </Tooltip>
              )}
              {hasHistoryError && record.errorMessage !== record.lastOperationError && (
                <Tooltip title={record.errorMessage}>
                  <Tag color="orange" style={{ cursor: 'pointer' }}>
                    历史错误
                  </Tag>
                </Tooltip>
              )}
            </div>
          );
        }
        return <Tag color="green">正常</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (time) => moment(time).format('MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          {record.status === 'PENDING' && (
            <Tooltip title="开启窗口">
              <Button
                type="link"
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={() => handleOpenWindow(record.id)}
              />
            </Tooltip>
          )}
          {record.status === 'ACTIVE' && (
            <Tooltip title="关闭窗口">
              <Button
                type="link"
                size="small"
                icon={<StopOutlined />}
                onClick={() => handleCloseWindow(record.id)}
              />
            </Tooltip>
          )}
          {((record.errorMessage && record.errorMessage.trim() !== '') ||
            (record.lastOperationError && record.lastOperationError.trim() !== '')) && (
            <Tooltip title="清除错误信息">
              <Button
                type="link"
                size="small"
                icon={<ClearOutlined />}
                onClick={() => handleClearError(record.id)}
                style={{ color: '#ff4d4f' }}
              />
            </Tooltip>
          )}

        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: isMobileView ? '12px' : '24px' }}>
      {/* 移动端滚动提示 */}
      {isMobileView && (
        <div style={{
          marginBottom: '16px',
          padding: '8px 12px',
          background: '#f0f2f5',
          borderRadius: '6px',
          fontSize: '12px',
          color: '#666'
        }}>
          👈 表格可左右滑动查看所有信息
        </div>
      )}

      {/* 统计信息 */}
      <Row gutter={isMobileView ? 8 : 16} style={{ marginBottom: isMobileView ? 16 : 24 }}>
        <Col xs={12} sm={6}>
          <Card size={isMobileView ? 'small' : 'default'}>
            <Statistic
              title="总窗口数"
              value={statistics.totalCount || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ fontSize: isMobileView ? '20px' : '24px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size={isMobileView ? 'small' : 'default'}>
            <Statistic
              title="待开启"
              value={statistics.statusStats?.PENDING || 0}
              valueStyle={{ color: '#fa8c16', fontSize: isMobileView ? '20px' : '24px' }}
              prefix={<Badge status="warning" />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size={isMobileView ? 'small' : 'default'}>
            <Statistic
              title="使用中"
              value={statistics.statusStats?.ACTIVE || 0}
              valueStyle={{ color: '#52c41a', fontSize: isMobileView ? '20px' : '24px' }}
              prefix={<Badge status="processing" />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size={isMobileView ? 'small' : 'default'}>
            <Statistic
              title="已关闭"
              value={statistics.statusStats?.CLOSED || 0}
              valueStyle={{ color: '#666', fontSize: isMobileView ? '20px' : '24px' }}
              prefix={<Badge status="default" />}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card
        title={
          <Space>
            <ClockCircleOutlined />
            使用窗口查询
            <Tooltip title="查询和管理Join Account的使用窗口">
              <InfoCircleOutlined style={{ color: '#1890ff' }} />
            </Tooltip>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                fetchWindows();
                fetchStatistics();
              }}
            >
              刷新
            </Button>
          </Space>
        }
      >
        {/* 筛选条件 */}
        <div style={{
          marginBottom: isMobileView ? 12 : 16,
          padding: isMobileView ? 12 : 16,
          background: '#fafafa',
          borderRadius: 6
        }}>
          <Row gutter={isMobileView ? [8, 8] : 16}>
            <Col xs={24} sm={12} md={6}>
              <Input
                placeholder="Token编号"
                prefix={<SearchOutlined />}
                value={filters.tokenNumber}
                onChange={(e) => setFilters(prev => ({ ...prev, tokenNumber: e.target.value }))}
                allowClear
                size={isMobileView ? 'middle' : 'default'}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Input
                placeholder="Zoom账号"
                value={filters.zoomUserId}
                onChange={(e) => setFilters(prev => ({ ...prev, zoomUserId: e.target.value }))}
                allowClear
                size={isMobileView ? 'middle' : 'default'}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Select
                placeholder="窗口状态"
                value={filters.status}
                onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
                allowClear
                style={{ width: '100%' }}
                size={isMobileView ? 'middle' : 'default'}
              >
                <Option value="PENDING">待开启</Option>
                <Option value="ACTIVE">使用中</Option>
                <Option value="CLOSED">已关闭</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <RangePicker
                placeholder={['开始时间', '结束时间']}
                value={filters.timeRange}
                onChange={(dates) => {
                  if (dates) {
                    setFilters(prev => ({
                      ...prev,
                      startTime: dates[0].toISOString(),
                      endTime: dates[1].toISOString(),
                      timeRange: dates
                    }));
                  } else {
                    setFilters(prev => {
                      const { startTime, endTime, timeRange, ...rest } = prev;
                      return rest;
                    });
                  }
                }}
                style={{ width: '100%' }}
                size={isMobileView ? 'middle' : 'default'}
              />
            </Col>
          </Row>
        </div>

        <div data-page="join-account-windows">
          <Table
            columns={columns}
            dataSource={windows}
            rowKey="id"
            loading={loading}
            scroll={{ x: 'max-content' }}
            size={isMobileView ? 'small' : 'default'}
            pagination={{
              ...pagination,
              showSizeChanger: !isMobileView,
              showQuickJumper: !isMobileView,
              showTotal: (total) => `共 ${total} 条记录`,
              simple: isMobileView,
              onChange: (page, pageSize) => {
                setPagination(prev => ({ ...prev, current: page, pageSize }));
              },
            }}
          />
        </div>
      </Card>
    </div>
  );
};

export default JoinAccountWindowQuery;
