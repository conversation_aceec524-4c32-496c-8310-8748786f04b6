import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  message,
  Space,
  Tag,
  Popconfirm,
  Card,
  Row,
  Col,
  Tooltip,
  Typography
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  SearchOutlined,
  SyncOutlined,
  UserAddOutlined
} from '@ant-design/icons';
import { zoomAuthApi } from '../services/api';

const { Option } = Select;
const { Search } = Input;
const { Title } = Typography;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const ZoomAuthManagement = () => {
  const [zoomAuths, setZoomAuths] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAuth, setEditingAuth] = useState(null);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [syncLoading, setSyncLoading] = useState(false);
  const [isMobileView, setIsMobileView] = useState(isMobile());

  // 获取认证列表
  const fetchZoomAuths = async (page = 1, size = 10, keyword = '') => {
    setLoading(true);
    try {
      const params = {
        page: page - 1,
        size,
        ...(keyword && { keyword })
      };

      const response = await zoomAuthApi.searchZoomAuth(params);

      // 验证响应数据格式
      if (response.data && Array.isArray(response.data.content)) {
        setZoomAuths(response.data.content);
        setPagination({
          current: page,
          pageSize: size,
          total: response.data.totalElements || 0
        });
      } else {
        console.error('Invalid response format:', response.data);
        message.error('获取认证信息失败：数据格式错误');
        setZoomAuths([]);
        setPagination({
          current: page,
          pageSize: size,
          total: 0
        });
      }
    } catch (error) {
      message.error('获取认证信息失败: ' + (error.response?.data?.message || error.message));
      console.error('Error fetching zoom auths:', error);
      setZoomAuths([]);
      setPagination({
        current: page,
        pageSize: size,
        total: 0
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchZoomAuths();
  }, []);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 创建或更新认证信息
  const handleSubmit = async (values) => {
    try {
      if (editingAuth) {
        await zoomAuthApi.updateZoomAuth(editingAuth.id, values);
        message.success('更新认证信息成功');
      } else {
        await zoomAuthApi.createZoomAuth(values);
        message.success('创建认证信息成功');
      }
      setModalVisible(false);
      setEditingAuth(null);
      form.resetFields();
      fetchZoomAuths(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error(editingAuth ? '更新认证信息失败' : '创建认证信息失败');
      console.error('Error saving zoom auth:', error);
    }
  };

  // 删除认证信息
  const handleDelete = async (id) => {
    try {
      await zoomAuthApi.deleteZoomAuth(id);
      message.success('删除认证信息成功');
      fetchZoomAuths(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('删除认证信息失败: ' + (error.response?.data?.message || error.message));
      console.error('Error deleting zoom auth:', error);
    }
  };

  // 刷新Token
  const handleRefreshToken = async (id) => {
    try {
      setLoading(true);
      await zoomAuthApi.refreshToken(id);
      message.success('刷新Token成功');
      fetchZoomAuths(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('刷新Token失败: ' + (error.response?.data?.message || error.message));
      console.error('Error refreshing token:', error);
    } finally {
      setLoading(false);
    }
  };

  // 批量刷新所有Token
  const handleRefreshAllTokens = async () => {
    try {
      setLoading(true);
      await zoomAuthApi.refreshAllTokens();
      message.success('批量刷新任务已启动');
      setTimeout(() => {
        fetchZoomAuths(pagination.current, pagination.pageSize);
      }, 2000);
    } catch (error) {
      message.error('批量刷新失败');
      console.error('Error refreshing all tokens:', error);
    } finally {
      setLoading(false);
    }
  };

  // 更新状态
  const handleUpdateStatus = async (id, status) => {
    try {
      await zoomAuthApi.updateZoomAuthStatus(id, status);
      message.success('更新状态成功');
      fetchZoomAuths(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('更新状态失败: ' + (error.response?.data?.message || error.message));
      console.error('Error updating status:', error);
    }
  };

  // 同步单个ZoomAuth的子账号信息
  const handleSyncUsers = async (id, accountName) => {
    try {
      setSyncLoading(true);
      const response = await zoomAuthApi.syncUsers(id);
      const result = response.data;

      if (result.success) {
        const { totalUsers, newUsers, updatedUsers, skippedUsers, errors } = result.result;
        let successMsg = `同步完成！总用户: ${totalUsers}, 新增: ${newUsers}, 更新: ${updatedUsers}, 跳过: ${skippedUsers}`;
        if (errors && errors.length > 0) {
          successMsg += `, 错误: ${errors.length}个`;
        }
        message.success(successMsg);
      } else {
        message.error(`同步失败: ${result.message}`);
      }
    } catch (error) {
      message.error('同步子账号失败');
      console.error('Error syncing users:', error);
    } finally {
      setSyncLoading(false);
    }
  };

  // 批量同步多个ZoomAuth的子账号信息
  const handleBatchSyncUsers = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要同步的认证账号');
      return;
    }

    try {
      setSyncLoading(true);
      const response = await zoomAuthApi.batchSyncUsers(selectedRowKeys);
      const results = response.data;

      let successCount = 0;
      let errorCount = 0;
      let totalNewUsers = 0;
      let totalUpdatedUsers = 0;

      results.forEach(result => {
        if (result.success) {
          successCount++;
          totalNewUsers += result.result.newUsers;
          totalUpdatedUsers += result.result.updatedUsers;
        } else {
          errorCount++;
        }
      });

      let msg = `批量同步完成！成功: ${successCount}个账号, 失败: ${errorCount}个账号`;
      msg += `, 总计新增用户: ${totalNewUsers}, 更新用户: ${totalUpdatedUsers}`;

      if (errorCount > 0) {
        message.warning(msg);
      } else {
        message.success(msg);
      }

      setSelectedRowKeys([]);
    } catch (error) {
      message.error('批量同步失败');
      console.error('Error batch syncing users:', error);
    } finally {
      setSyncLoading(false);
    }
  };

  // 打开编辑模态框
  const handleEdit = async (record) => {
    try {
      setLoading(true);
      // 使用专门的编辑端点获取包含敏感字段的数据
      const response = await zoomAuthApi.getZoomAuthForEdit(record.id);
      const editData = response.data;

      setEditingAuth(record);
      form.setFieldsValue({
        ...editData,
        // 保留原有的clientSecret值，让密码框正确显示星号
      });
      setModalVisible(true);
    } catch (error) {
      message.error('获取编辑数据失败');
      console.error('Error fetching edit data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 打开新建模态框
  const handleAdd = () => {
    setEditingAuth(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 搜索
  const handleSearch = (value) => {
    fetchZoomAuths(1, pagination.pageSize, value);
  };

  // 状态标签颜色
  const getStatusColor = (status) => {
    const colors = {
      ACTIVE: 'green',
      EXPIRED: 'orange',
      ERROR: 'red',
      DISABLED: 'gray'
    };
    return colors[status] || 'default';
  };

  // 认证类型标签颜色
  const getAuthTypeColor = (authType) => {
    const colors = {
      OAUTH2: 'blue',
      JWT: 'purple',
      SERVER_TO_SERVER: 'cyan'
    };
    return colors[authType] || 'default';
  };

  // 检查Token是否即将过期
  const isTokenExpiringSoon = (tokenExpiresAt) => {
    if (!tokenExpiresAt) return false;
    const expiryTime = new Date(tokenExpiresAt);
    const now = new Date();
    const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);
    return expiryTime <= fiveMinutesFromNow;
  };

  const columns = [
    {
      title: '账号名称',
      dataIndex: 'accountName',
      key: 'accountName',
      width: 150,
    },
    {
      title: 'Zoom账号ID',
      dataIndex: 'zoomAccountId',
      key: 'zoomAccountId',
      width: 150,
    },
    {
      title: '主账号邮箱',
      dataIndex: 'primaryEmail',
      key: 'primaryEmail',
      width: 180,
      ellipsis: true,
    },
    {
      title: '认证类型',
      dataIndex: 'authType',
      key: 'authType',
      width: 120,
      render: (authType) => (
        <Tag color={getAuthTypeColor(authType)}>
          {authType === 'OAUTH2' ? 'OAuth2.0' : 
           authType === 'JWT' ? 'JWT' : 
           authType === 'SERVER_TO_SERVER' ? 'Server-to-Server' : authType}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status === 'ACTIVE' ? '正常' :
           status === 'EXPIRED' ? '已过期' :
           status === 'ERROR' ? '错误' :
           status === 'DISABLED' ? '已禁用' : status}
        </Tag>
      ),
    },
    {
      title: 'Token状态',
      key: 'tokenStatus',
      width: 120,
      render: (_, record) => {
        if (!record.tokenExpiresAt) {
          return <Tag color="gray">未获取</Tag>;
        }
        
        const isExpired = new Date(record.tokenExpiresAt) <= new Date();
        const isExpiringSoon = isTokenExpiringSoon(record.tokenExpiresAt);
        
        if (isExpired) {
          return <Tag color="red">已过期</Tag>;
        } else if (isExpiringSoon) {
          return <Tag color="orange">即将过期</Tag>;
        } else {
          return <Tag color="green">正常</Tag>;
        }
      },
    },
    {
      title: 'Token过期时间',
      dataIndex: 'tokenExpiresAt',
      key: 'tokenExpiresAt',
      width: 180,
      render: (tokenExpiresAt) => {
        if (!tokenExpiresAt) return '-';
        return new Date(tokenExpiresAt).toLocaleString();
      },
    },
    {
      title: '刷新次数',
      dataIndex: 'refreshCount',
      key: 'refreshCount',
      width: 100,
      render: (count) => count || 0,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 180 : 280, // 参照PMI管理页面的宽度设置
      fixed: isMobileView ? false : 'right', // PC端固定操作栏
      render: (_, record) => {
        // 移动端双列展示
        const actionGroups = [
          // 移动端：双列布局
          [
            <Tooltip key="edit" title="编辑认证信息">
              <Button
                type="default"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
                style={{
                  fontSize: '11px',
                  width: '70px',
                  height: '28px',
                  padding: '4px 6px'
                }}
              >
                编辑
              </Button>
            </Tooltip>,
            <Tooltip key="refresh" title="刷新Token">
              <Button
                type="default"
                icon={<SyncOutlined />}
                size="small"
                onClick={() => handleRefreshToken(record.id)}
                loading={loading}
                style={{
                  fontSize: '11px',
                  width: '70px',
                  height: '28px',
                  padding: '4px 6px'
                }}
              >
                刷新
              </Button>
            </Tooltip>
          ],
          [
            <Tooltip key="sync" title="同步子账号">
              <Button
                type="primary"
                icon={<UserAddOutlined />}
                size="small"
                onClick={() => handleSyncUsers(record.id, record.accountName)}
                loading={syncLoading}
                style={{
                  fontSize: '11px',
                  width: '70px',
                  height: '28px',
                  padding: '4px 6px'
                }}
              >
                同步
              </Button>
            </Tooltip>,
            <Popconfirm
              key="delete"
              title="确定要删除这个认证信息吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
              placement="top"
            >
              <Tooltip title="删除认证信息">
                <Button
                  type="primary"
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                  style={{
                    fontSize: '11px',
                    width: '70px',
                    height: '28px',
                    padding: '4px 6px'
                  }}
                >
                  删除
                </Button>
              </Tooltip>
            </Popconfirm>
          ]
        ];

        // PC端：固定展示，一行最多3个按钮
        const pcActionGroups = [
          // 第一行：编辑、刷新Token、同步子账号
          [
            <Tooltip key="edit" title="编辑认证信息">
              <Button
                type="link"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              >
                编辑
              </Button>
            </Tooltip>,
            <Tooltip key="refresh" title="刷新Token">
              <Button
                type="link"
                icon={<SyncOutlined />}
                size="small"
                onClick={() => handleRefreshToken(record.id)}
                loading={loading}
              >
                刷新Token
              </Button>
            </Tooltip>,
            <Tooltip key="sync" title="同步子账号">
              <Button
                type="link"
                icon={<UserAddOutlined />}
                size="small"
                onClick={() => handleSyncUsers(record.id, record.accountName)}
                loading={syncLoading}
              >
                同步子账号
              </Button>
            </Tooltip>
          ],
          // 第二行：删除
          [
            <Popconfirm
              key="delete"
              title="确定要删除这个认证信息吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
              placement="topRight"
            >
              <Tooltip title="删除认证信息">
                <Button
                  type="link"
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                >
                  删除
                </Button>
              </Tooltip>
            </Popconfirm>
          ]
        ];

        return isMobileView ? (
          // 移动端：分行显示
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '4px',
            alignItems: 'flex-start'
          }}>
            {actionGroups.map((group, groupIndex) => (
              <div key={groupIndex} style={{
                display: 'flex',
                gap: '4px',
                alignItems: 'center'
              }}>
                {group.filter(Boolean).map((button, buttonIndex) => (
                  <span key={buttonIndex}>{button}</span>
                ))}
              </div>
            ))}
          </div>
        ) : (
          // PC端：固定展示，一行最多3个按钮
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '4px',
            alignItems: 'flex-start'
          }}>
            {pcActionGroups.map((group, groupIndex) => (
              <div key={groupIndex} style={{
                display: 'flex',
                gap: '4px',
                alignItems: 'center'
              }}>
                {group.filter(Boolean).map((button, buttonIndex) => (
                  <span key={buttonIndex}>{button}</span>
                ))}
              </div>
            ))}
          </div>
        );
      },
    },
  ];

  return (
    <div style={{ padding: isMobileView ? '16px' : '24px' }}>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col xs={24} sm={24} md={12} lg={8} xl={8}>
            <Title level={4}>Zoom主账号管理</Title>
          </Col>
          <Col xs={24} sm={24} md={12} lg={16} xl={16}>
            <div className={isMobileView ? 'zoom-auth-actions' : ''}>
              <Space wrap size={isMobileView ? [8, 8] : 'middle'}>
                <Search
                  placeholder="搜索账号名称"
                  allowClear
                  onSearch={handleSearch}
                  style={{ width: isMobileView ? '100%' : 200 }}
                  size={isMobileView ? 'middle' : 'middle'}
                />
                <Button
                  type="primary"
                  icon={<SyncOutlined />}
                  onClick={handleRefreshAllTokens}
                  loading={loading}
                  size={isMobileView ? 'middle' : 'middle'}
                >
                  {isMobileView ? '批量刷新' : '批量刷新Token'}
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                  size={isMobileView ? 'middle' : 'middle'}
                >
                  新建认证
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => fetchZoomAuths(pagination.current, pagination.pageSize)}
                  size={isMobileView ? 'middle' : 'middle'}
                >
                  刷新
                </Button>
                <Button
                  type="primary"
                  icon={<UserAddOutlined />}
                  onClick={handleBatchSyncUsers}
                  loading={syncLoading}
                  disabled={selectedRowKeys.length === 0}
                  size={isMobileView ? 'middle' : 'middle'}
                >
                  {isMobileView ? `批量同步 (${selectedRowKeys.length})` : `批量同步子账号 (${selectedRowKeys.length})`}
                </Button>
              </Space>
            </div>
          </Col>
        </Row>

        <div data-page="zoom-auth">
          <Table
            columns={columns}
            dataSource={zoomAuths}
            rowKey="id"
            loading={loading}
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
              getCheckboxProps: (record) => ({
                disabled: record.status !== 'ACTIVE', // 只允许选择状态为ACTIVE的记录
              }),
            }}
            pagination={{
              ...pagination,
              showSizeChanger: !isMobileView,
              showQuickJumper: !isMobileView,
              showTotal: (total, range) =>
                isMobileView
                  ? `${range[0]}-${range[1]} / ${total}`
                  : `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, size) => fetchZoomAuths(page, size),
              onShowSizeChange: (current, size) => fetchZoomAuths(1, size),
              size: isMobileView ? 'small' : 'default',
            }}
            scroll={{ x: isMobileView ? 800 : 1200 }}
            size={isMobileView ? 'small' : 'default'}
          />
        </div>
      </Card>

      <Modal
        title={editingAuth ? '编辑认证信息' : '新建认证信息'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingAuth(null);
          form.resetFields();
        }}
        footer={null}
        width={isMobileView ? '95%' : 600}
        style={isMobileView ? { top: 20 } : {}}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="accountName"
            label="账号名称"
            rules={[{ required: true, message: '请输入账号名称' }]}
          >
            <Input placeholder="请输入账号名称" />
          </Form.Item>

          <Form.Item
            name="zoomAccountId"
            label="Zoom账号ID"
            rules={[{ required: true, message: '请输入Zoom账号ID' }]}
          >
            <Input placeholder="请输入Zoom账号ID" />
          </Form.Item>

          <Form.Item
            name="primaryEmail"
            label="主账号邮箱"
            rules={[
              { required: true, message: '请输入主账号邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input placeholder="请输入主账号邮箱地址" />
          </Form.Item>

          <Form.Item
            name="clientId"
            label="客户端ID"
            rules={[{ required: true, message: '请输入客户端ID' }]}
          >
            <Input placeholder="请输入客户端ID" />
          </Form.Item>

          <Form.Item
            name="clientSecret"
            label="客户端密钥"
            rules={[{ required: true, message: '请输入客户端密钥' }]}
          >
            <Input.Password placeholder="请输入客户端密钥" />
          </Form.Item>

          <Form.Item
            name="authType"
            label="认证类型"
            rules={[{ required: true, message: '请选择认证类型' }]}
          >
            <Select placeholder="请选择认证类型">
              <Option value="OAUTH2">OAuth2.0</Option>
              <Option value="JWT">JWT</Option>
              <Option value="SERVER_TO_SERVER">Server-to-Server OAuth</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="webhookSecretToken"
            label="Webhook密钥令牌"
          >
            <Input placeholder="请输入Webhook密钥令牌（可选）" />
          </Form.Item>

          <Form.Item
            name="apiBaseUrl"
            label="API基础URL"
            initialValue="https://api.zoom.us/v2"
          >
            <Input placeholder="请输入API基础URL" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={3} placeholder="请输入描述信息（可选）" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingAuth ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 自定义样式 */}
      <style jsx>{`
        /* 响应式按钮布局优化 */
        @media (max-width: 992px) {
          :global(.zoom-auth-actions) {
            justify-content: flex-start !important;
          }
        }

        @media (max-width: 768px) {
          :global(.ant-btn) {
            margin-bottom: 8px !important;
          }

          :global(.ant-space) {
            flex-wrap: wrap !important;
          }

          :global(.zoom-auth-actions) {
            justify-content: flex-start !important;
            margin-top: 8px;
          }

          /* 操作按钮样式 */
          :global(.mobile-action-buttons .ant-btn) {
            padding: 4px 6px !important;
            font-size: 12px !important;
            height: auto !important;
            min-width: auto !important;
          }

          :global(.mobile-action-buttons .ant-space-item) {
            margin-right: 4px !important;
          }

          /* 表格操作列样式 */
          :global(.action-column) {
            text-align: center !important;
          }
        }

        @media (max-width: 576px) {
          :global(.zoom-auth-actions .ant-btn) {
            flex: 1 1 auto !important;
            min-width: 120px !important;
            margin-bottom: 8px !important;
          }

          :global(.ant-btn-group .ant-btn) {
            flex: none !important;
            width: auto !important;
          }

          :global(.zoom-auth-actions) {
            justify-content: stretch !important;
          }

          :global(.zoom-auth-actions .ant-input-search) {
            width: 100% !important;
            margin-bottom: 8px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default ZoomAuthManagement;
