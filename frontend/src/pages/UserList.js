import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Table, Button, Modal, Form, Input, Select, Space, Tag, message, Popconfirm, Card, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SettingOutlined, UnorderedListOutlined } from '@ant-design/icons';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { userApi, pmiApi } from '../services/api';
import dayjs from 'dayjs';

const { Option } = Select;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const UserList = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [highlightUserId, setHighlightUserId] = useState(null);
  const [filterUserId, setFilterUserId] = useState(null);
  const [allUsers, setAllUsers] = useState([]); // 存储所有用户数据
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [userPmiStats, setUserPmiStats] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [form] = Form.useForm();
  const isInitializedRef = useRef(false);

  // 从用户数据中提取PMI统计信息
  const extractUserPmiStats = (userList) => {
    const stats = {};
    userList.forEach(user => {
      // 用户列表接口现在直接返回pmiCount字段
      stats[user.id] = user.pmiCount || 0;
    });
    setUserPmiStats(stats);
  };

  // 统一的用户加载函数，避免重复代码
  const loadUsers = useCallback(async (filterUserIdParam = null, page = 1, pageSize = 20) => {
    try {
      console.log('🔄 [UserList] 开始加载用户数据...');
      console.log('📱 [UserList] 当前是否为移动端视图:', isMobileView);
      console.log('🔍 [UserList] 筛选参数:', { filterUserIdParam, page, pageSize });
      setLoading(true);

      let userData = [];
      let totalElements = 0;

      // 如果有筛选条件，先获取所有数据进行筛选
      if (filterUserIdParam !== null && filterUserIdParam !== undefined) {
        // 获取所有用户数据进行筛选
        const response = await userApi.getUsers({ page: 0, size: 1000 });
        const allUserData = response.data.content || response.data || [];
        setAllUsers(allUserData);

        const filteredUsers = allUserData.filter(user => user.id === filterUserIdParam);
        userData = filteredUsers;
        totalElements = filteredUsers.length;

        if (filteredUsers.length > 0) {
          setHighlightUserId(filterUserIdParam);
          // 5秒后取消高亮
          setTimeout(() => {
            setHighlightUserId(null);
          }, 5000);
        }
      } else {
        // 没有筛选条件时使用分页加载
        const response = await userApi.getUsers({ page: page - 1, size: pageSize });
        userData = response.data.content || response.data || [];
        totalElements = response.data.totalElements || userData.length;
        setAllUsers(userData);
        setHighlightUserId(null);
      }

      console.log('✅ [UserList] 用户数据加载成功:', {
        userCount: userData.length,
        totalElements,
        currentPage: page,
        isMobileView
      });

      setUsers(userData);
      setPagination({
        current: page,
        pageSize: pageSize,
        total: totalElements,
      });

      // 从用户数据中提取PMI统计信息
      extractUserPmiStats(userData);
    } catch (error) {
      console.error('❌ [UserList] 加载用户列表失败:', error);
      console.error('🔍 [UserList] 错误详情:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        isMobileView
      });
      message.error('加载用户列表失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    // 防止重复初始化
    if (isInitializedRef.current) {
      return;
    }

    // 立即设置为已初始化，防止并发调用
    isInitializedRef.current = true;

    // 检查URL路径参数，如果有userId参数，则设置筛选条件
    // 支持两种格式：/users?userId=1 和 /users/1
    const searchParams = new URLSearchParams(location.search);
    const queryUserId = searchParams.get('userId');

    // 优先使用路径参数（React Router params）
    const pathUserId = params.userId;

    let userId = null;
    if (pathUserId) {
      // 优先使用路径参数
      userId = pathUserId;
    } else if (queryUserId) {
      // 兼容查询参数格式
      userId = queryUserId;
    }

    // 使用统一的加载函数
    const userIdNum = userId ? parseInt(userId) : null;
    setFilterUserId(userIdNum);
    loadUsers(userIdNum, 1, 20);
  }, [location.search, params.userId]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleSearch = async () => {
    if (!searchText.trim()) {
      loadUsers(1, pagination.pageSize);
      return;
    }

    try {
      setLoading(true);
      const response = await userApi.searchUsers(searchText);
      console.log('搜索用户响应:', response);
      const searchResults = response.data || [];
      setUsers(searchResults);

      // 从搜索结果中提取PMI统计信息
      if (searchResults.length > 0) {
        extractUserPmiStats(searchResults);
      }
    } catch (error) {
      console.error('搜索用户失败:', error);
      message.error('搜索用户失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingUser(null);
    setModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (user) => {
    setEditingUser(user);
    setModalVisible(true);
    form.setFieldsValue(user);
  };

  // 处理PMI管理按钮点击
  const handlePmiManagement = (record) => {
    const userId = record.id;
    const pmiCount = userPmiStats[userId] || 0;

    if (pmiCount > 0) {
      // 用户有PMI，跳转到PMI列表页面
      navigate(`/pmi-management/user/${userId}`);
    } else {
      // 用户没有PMI，跳转到PMI管理页面并打开创建弹窗
      navigate(`/pmi-management/user/${userId}/create`);
    }
  };

  const handleDelete = async (id) => {
    try {
      await userApi.deleteUser(id);
      message.success('删除成功');
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('删除用户失败:', error);
    }
  };

  const handleStatusChange = async (id, status) => {
    try {
      await userApi.updateUserStatus(id, status);
      message.success('状态更新成功');
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('更新状态失败:', error);
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingUser) {
        await userApi.updateUser(editingUser.id, values);
        message.success('更新成功');
      } else {
        await userApi.createUser(values);
        message.success('创建成功');
      }
      setModalVisible(false);
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('保存用户失败:', error);
    }
  };

  // 构建完整的列配置
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: isMobileView ? 60 : 70,
      render: (text) => (
        <div style={{
          fontSize: isMobileView ? '12px' : '14px',
          fontWeight: 'bold',
          color: '#1890ff',
          lineHeight: isMobileView ? '1.2' : '1.4'
        }}>
          {text}
        </div>
      ),
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: isMobileView ? 100 : 120,
      ellipsis: true,
      render: (text, record) => (
        <a
          href={`/users/${record.id}`}
          style={{
            fontSize: isMobileView ? '12px' : '14px',
            color: '#1890ff',
            textDecoration: 'none',
            lineHeight: isMobileView ? '1.2' : '1.4'
          }}
          onMouseEnter={(e) => e.target.style.textDecoration = 'underline'}
          onMouseLeave={(e) => e.target.style.textDecoration = 'none'}
        >
          {text}
        </a>
      ),
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      key: 'fullName',
      width: isMobileView ? 100 : 120,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '12px' : '14px' }}>
          {text || '-'}
        </span>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: isMobileView ? 150 : 180,
      ellipsis: true,
      render: (text) => (
        <div style={{
          fontSize: isMobileView ? '11px' : '14px',
          lineHeight: isMobileView ? '1.2' : '1.4'
        }}>
          {text}
        </div>
      ),
    },

    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
      width: isMobileView ? 120 : 120,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '12px' : '14px' }}>
          {text || '-'}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: isMobileView ? 90 : 120,
      render: (status, record) => {
        const statusConfig = {
          ACTIVE: { color: 'green', text: '活跃' },
          INACTIVE: { color: 'default', text: '非活跃' },
          SUSPENDED: { color: 'red', text: '暂停' },
        };
        const config = statusConfig[status] || { color: 'default', text: status };

        if (isMobileView) {
          // 移动端只显示标签，不可编辑
          return (
            <Tag
              color={config.color}
              style={{
                fontSize: '10px',
                margin: 0,
                padding: '2px 6px'
              }}
            >
              {config.text}
            </Tag>
          );
        }

        return (
          <Select
            value={status}
            size="small"
            style={{ width: 80, fontSize: '14px' }}
            onChange={(value) => handleStatusChange(record.id, value)}
          >
            <Option value="ACTIVE">
              <Tag color="green" style={{ fontSize: '12px' }}>活跃</Tag>
            </Option>
            <Option value="INACTIVE">
              <Tag color="default" style={{ fontSize: '12px' }}>非活跃</Tag>
            </Option>
            <Option value="SUSPENDED">
              <Tag color="red" style={{ fontSize: '12px' }}>暂停</Tag>
            </Option>
          </Select>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: isMobileView ? 120 : 150,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {dayjs(text).format(isMobileView ? 'MM-DD HH:mm' : 'YYYY-MM-DD HH:mm')}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 200 : 280,
      fixed: isMobileView ? false : 'right',
      render: (_, record) => {
        const actionGroups = isMobileView ? [
          // 移动端：第一行编辑和删除
          [
            <Button
              key="edit"
              type="default"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEdit(record)}
              style={{
                fontSize: '11px',
                width: '70px',
                height: '28px',
                padding: '4px 6px'
              }}
            >
              编辑
            </Button>,
            <Popconfirm
              key="delete"
              title="确定要删除这个用户吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
                size="small"
                style={{
                  fontSize: '11px',
                  width: '70px',
                  height: '28px',
                  padding: '4px 6px'
                }}
              >
                删除
              </Button>
            </Popconfirm>
          ],
          // 移动端：第二行PMI按钮，保留文字，增加宽度
          [
            <Button
              key="pmi"
              type="default"
              icon={(userPmiStats[record.id] || 0) > 0 ? <UnorderedListOutlined /> : <PlusOutlined />}
              size="small"
              onClick={() => handlePmiManagement(record)}
              style={{
                fontSize: '11px',
                width: '148px', // 增加宽度，覆盖两个按钮的宽度
                height: '28px',
                padding: '4px 6px',
                color: (userPmiStats[record.id] || 0) > 0 ? '#1890ff' : '#52c41a'
              }}
            >
              {(userPmiStats[record.id] || 0) > 0 ? 'PMI列表' : '创建PMI'}
            </Button>
          ]
        ] : [
          // PC端：第一行编辑和删除
          [
            <Button
              key="edit"
              type="default"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEdit(record)}
              style={{
                fontSize: '12px',
                width: 'auto',
                height: '28px',
                padding: '4px 8px'
              }}
            >
              编辑
            </Button>,
            <Popconfirm
              key="delete"
              title="确定要删除这个用户吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
                size="small"
                style={{
                  fontSize: '12px',
                  width: 'auto',
                  height: '28px',
                  padding: '4px 8px'
                }}
              >
                删除
              </Button>
            </Popconfirm>
          ],
          // PC端：第二行PMI按钮，保留文字，增加宽度
          [
            <Button
              key="pmi"
              type="default"
              icon={(userPmiStats[record.id] || 0) > 0 ? <UnorderedListOutlined /> : <PlusOutlined />}
              size="small"
              onClick={() => handlePmiManagement(record)}
              style={{
                fontSize: '12px',
                width: '120px', // 增加宽度
                height: '28px',
                padding: '4px 8px',
                color: (userPmiStats[record.id] || 0) > 0 ? '#1890ff' : '#52c41a'
              }}
            >
              {(userPmiStats[record.id] || 0) > 0 ? 'PMI列表' : '创建PMI'}
            </Button>
          ]
        ];

        return (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '4px',
            alignItems: isMobileView ? 'flex-start' : 'center'
          }}>
            {actionGroups.map((group, groupIndex) => (
              <div
                key={groupIndex}
                style={{
                  display: 'flex',
                  gap: '4px',
                  justifyContent: isMobileView ? 'flex-start' : 'center'
                }}
              >
                {group}
              </div>
            ))}
          </div>
        );
      },
    }
  ];

  // 清除筛选
  const clearFilter = () => {
    navigate('/users');
  };

  return (
    <div data-page="users" style={{ padding: isMobileView ? '12px' : '24px' }}>


      <div style={{
        marginBottom: isMobileView ? 12 : 16,
        display: 'flex',
        flexDirection: isMobileView ? 'column' : 'row',
        justifyContent: 'space-between',
        gap: isMobileView ? '12px' : '0'
      }}>
        <div>
          <h1 style={{ fontSize: isMobileView ? '20px' : '24px', marginBottom: isMobileView ? '8px' : '16px' }}>
            用户管理
          </h1>
          {filterUserId && (
            <div style={{ marginTop: 8 }}>
              <span style={{ color: '#1890ff', marginRight: 8, fontSize: isMobileView ? '12px' : '14px' }}>
                正在显示用户ID为 {filterUserId} 的记录
              </span>
              <Button
                type="link"
                size="small"
                onClick={clearFilter}
                style={{ padding: 0, fontSize: isMobileView ? '12px' : '14px' }}
              >
                显示所有用户
              </Button>
            </div>
          )}
        </div>
        <Space direction={isMobileView ? 'vertical' : 'horizontal'} style={{ width: isMobileView ? '100%' : 'auto' }}>
          <Input.Search
            placeholder={isMobileView ? "搜索用户" : "搜索用户名或姓名"}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onSearch={handleSearch}
            style={{ width: isMobileView ? '100%' : 200 }}
            size={isMobileView ? 'middle' : 'middle'}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
            style={{ width: isMobileView ? '100%' : 'auto' }}
            size={isMobileView ? 'middle' : 'middle'}
          >
            新建用户
          </Button>
        </Space>
      </div>

      {/* 用户列表 */}
      <Card size={isMobileView ? 'small' : 'default'}>
        <div data-page="users">
          <Table
            columns={columns}
            dataSource={users}
            rowKey="id"
            loading={loading}
            size={isMobileView ? 'small' : 'middle'}
            scroll={{ x: 'max-content' }}
            pagination={{
              ...pagination,
              showSizeChanger: !isMobileView,
              showQuickJumper: !isMobileView,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              simple: isMobileView,
              size: isMobileView ? 'small' : 'default',
              onChange: (page, pageSize) => {
                loadUsers(filterUserId, page, pageSize);
              },
            }}
          />
        </div>
      </Card>

      <Modal
        title={editingUser ? '编辑用户' : '新建用户'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="fullName"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="department"
            label="部门"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="phone"
            label="电话"
          >
            <Input />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingUser ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserList;
