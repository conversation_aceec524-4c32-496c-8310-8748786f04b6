import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Typography, Divider, Tag, Alert, Spin } from 'antd';
import { ReloadOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import api from '../services/api';

const { Title, Text, Paragraph } = Typography;

const MobileDebug = () => {
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState({});
  const [systemInfo, setSystemInfo] = useState({});

  useEffect(() => {
    collectSystemInfo();
  }, []);

  const collectSystemInfo = () => {
    const info = {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      url: window.location.href,
      hostname: window.location.hostname,
      protocol: window.location.protocol,
      port: window.location.port,
      pathname: window.location.pathname,
      search: window.location.search,
      hash: window.location.hash,
      referrer: document.referrer,
      nodeEnv: process.env.NODE_ENV,
      apiBaseUrl: process.env.REACT_APP_API_BASE_URL,
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight,
      localStorage: !!window.localStorage,
      sessionStorage: !!window.sessionStorage,
      token: !!localStorage.getItem('token')
    };
    setSystemInfo(info);
  };

  const runAPITests = async () => {
    setLoading(true);
    const results = {};

    // 测试1: 基础连通性
    try {
      console.log('🧪 测试API基础连通性...');
      const response = await api.get('/health', { timeout: 5000 });
      results.connectivity = {
        success: true,
        status: response.status,
        data: response.data
      };
    } catch (error) {
      results.connectivity = {
        success: false,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      };
    }

    // 测试2: 用户API
    try {
      console.log('🧪 测试用户API...');
      const response = await api.get('/users?page=0&size=1', { timeout: 10000 });
      results.userApi = {
        success: true,
        status: response.status,
        dataLength: response.data?.content?.length || 0
      };
    } catch (error) {
      results.userApi = {
        success: false,
        error: error.message,
        status: error.response?.status
      };
    }

    // 测试3: Zoom用户API
    try {
      console.log('🧪 测试Zoom用户API...');
      const response = await api.get('/zoom-users?page=0&size=1', { timeout: 10000 });
      results.zoomUserApi = {
        success: true,
        status: response.status,
        dataLength: response.data?.content?.length || 0
      };
    } catch (error) {
      results.zoomUserApi = {
        success: false,
        error: error.message,
        status: error.response?.status
      };
    }

    // 测试4: 会议API
    try {
      console.log('🧪 测试会议API...');
      const response = await api.get('/meetings?page=0&size=1', { timeout: 10000 });
      results.meetingApi = {
        success: true,
        status: response.status,
        dataLength: response.data?.content?.length || 0
      };
    } catch (error) {
      results.meetingApi = {
        success: false,
        error: error.message,
        status: error.response?.status
      };
    }

    setTestResults(results);
    setLoading(false);
  };

  const renderTestResult = (testName, result) => {
    if (!result) return null;

    return (
      <Card size="small" style={{ marginBottom: 8 }}>
        <Space>
          {result.success ? (
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
          ) : (
            <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
          )}
          <Text strong>{testName}</Text>
          <Tag color={result.success ? 'success' : 'error'}>
            {result.success ? '成功' : '失败'}
          </Tag>
          {result.status && <Tag>状态: {result.status}</Tag>}
        </Space>
        {result.error && (
          <Paragraph style={{ marginTop: 8, marginBottom: 0 }}>
            <Text type="danger">错误: {result.error}</Text>
          </Paragraph>
        )}
        {result.dataLength !== undefined && (
          <Paragraph style={{ marginTop: 8, marginBottom: 0 }}>
            <Text>数据条数: {result.dataLength}</Text>
          </Paragraph>
        )}
      </Card>
    );
  };

  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  return (
    <div style={{ padding: '16px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>移动端调试工具</Title>
      
      <Alert
        message={`当前设备: ${isMobile ? '移动设备' : '桌面设备'}`}
        type={isMobile ? 'info' : 'warning'}
        style={{ marginBottom: 16 }}
      />

      <Card title="系统信息" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text><strong>URL:</strong> {systemInfo.url}</Text>
          <Text><strong>主机名:</strong> {systemInfo.hostname}</Text>
          <Text><strong>协议:</strong> {systemInfo.protocol}</Text>
          <Text><strong>环境:</strong> {systemInfo.nodeEnv}</Text>
          <Text><strong>API地址:</strong> {systemInfo.apiBaseUrl || '未设置'}</Text>
          <Text><strong>网络状态:</strong> {systemInfo.onLine ? '在线' : '离线'}</Text>
          <Text><strong>认证Token:</strong> {systemInfo.token ? '已设置' : '未设置'}</Text>
          <Text><strong>屏幕尺寸:</strong> {systemInfo.screenWidth} x {systemInfo.screenHeight}</Text>
          <Text><strong>视口尺寸:</strong> {systemInfo.viewportWidth} x {systemInfo.viewportHeight}</Text>
        </Space>
      </Card>

      <Card title="API连通性测试" style={{ marginBottom: 16 }}>
        <Space style={{ marginBottom: 16 }}>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />} 
            onClick={runAPITests}
            loading={loading}
          >
            运行测试
          </Button>
        </Space>

        {loading && <Spin tip="正在测试API连通性..." />}

        {Object.keys(testResults).length > 0 && (
          <div>
            {renderTestResult('基础连通性', testResults.connectivity)}
            {renderTestResult('用户API', testResults.userApi)}
            {renderTestResult('Zoom用户API', testResults.zoomUserApi)}
            {renderTestResult('会议API', testResults.meetingApi)}
          </div>
        )}
      </Card>

      <Card title="浏览器信息">
        <Paragraph>
          <Text code style={{ fontSize: '12px', wordBreak: 'break-all' }}>
            {systemInfo.userAgent}
          </Text>
        </Paragraph>
      </Card>
    </div>
  );
};

export default MobileDebug;
