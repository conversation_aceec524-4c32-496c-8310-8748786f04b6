import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Input,
  message,
  Modal,
  Form,
  InputNumber
} from 'antd';
import {
  ReloadOutlined,
  KeyOutlined,
  InfoCircleOutlined,
  EyeOutlined,
  SearchOutlined,
  CopyOutlined,
  SyncOutlined,
  UserOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { joinAccountPasswordApi } from '../services/api';
import moment from 'moment';

const { Option } = Select;
const { RangePicker } = DatePicker;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const JoinAccountPasswordLogs = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [statistics, setStatistics] = useState({});
  const [changePasswordModalVisible, setChangePasswordModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [filters, setFilters] = useState({});

  // 获取密码变更日志列表
  const fetchLogs = async (params = {}) => {
    setLoading(true);
    try {
      const queryParams = {
        page: pagination.current - 1,
        size: pagination.pageSize,
        ...filters,
        ...params,
      };
      
      const response = await joinAccountPasswordApi.getPasswordLogs(queryParams);
      if (response.data.success) {
        const data = response.data.data;
        setLogs(data.content || []);
        setPagination(prev => ({
          ...prev,
          total: data.totalElements || 0,
        }));
      }
    } catch (error) {
      message.error('获取密码变更日志失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const response = await joinAccountPasswordApi.getStatistics();
      if (response.data.success) {
        setStatistics(response.data.data);
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  };

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    fetchLogs();
    fetchStatistics();
  }, [pagination.current, pagination.pageSize, filters]);

  // 手动变更密码
  const handleChangePassword = async (values) => {
    try {
      const response = await joinAccountPasswordApi.changePassword(values.zoomUserId, {
        operator: 'ADMIN'
      });
      if (response.data.success) {
        message.success('密码变更成功');
        setChangePasswordModalVisible(false);
        form.resetFields();
        fetchLogs();
        fetchStatistics();
      }
    } catch (error) {
      message.error('密码变更失败');
    }
  };

  // 变更类型标签颜色
  const getChangeTypeColor = (changeType) => {
    const colors = {
      WINDOW_OPEN: 'green',
      WINDOW_CLOSE: 'orange',
      MANUAL: 'blue'
    };
    return colors[changeType] || 'default';
  };

  // 变更类型描述
  const getChangeTypeText = (changeType) => {
    const texts = {
      WINDOW_OPEN: '窗口开启',
      WINDOW_CLOSE: '窗口关闭',
      MANUAL: '手动变更'
    };
    return texts[changeType] || changeType;
  };

  // 变更类型图标
  const getChangeTypeIcon = (changeType) => {
    const icons = {
      WINDOW_OPEN: <SyncOutlined />,
      WINDOW_CLOSE: <SyncOutlined />,
      MANUAL: <UserOutlined />
    };
    return icons[changeType] || <KeyOutlined />;
  };

  const columns = [
    {
      title: isMobileView ? '账号' : 'Zoom账号',
      key: 'zoomAccount',
      width: isMobileView ? 120 : 200,
      render: (_, record) => {
        const zoomUser = record.zoomUser;
        if (zoomUser) {
          return (
            <div>
              <a
                href={`http://localhost:3000/zoom-users/${zoomUser.id}`}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: '#1890ff',
                  textDecoration: 'none',
                  fontSize: isMobileView ? '11px' : '14px'
                }}
              >
                {zoomUser.email}
              </a>
              {!isMobileView && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  ID: {zoomUser.id}
                </div>
              )}
            </div>
          );
        }
        return (
          <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
            ID: {record.log?.zoomUserId || '-'}
          </span>
        );
      },
    },
    {
      title: isMobileView ? '类型' : '变更类型',
      dataIndex: ['log', 'changeType'],
      key: 'changeType',
      width: isMobileView ? 70 : 120,
      render: (changeType) => (
        <Tag
          color={getChangeTypeColor(changeType)}
          icon={getChangeTypeIcon(changeType)}
          style={{ fontSize: isMobileView ? '10px' : '12px' }}
        >
          {getChangeTypeText(changeType)}
        </Tag>
      ),
    },
    // 移动端隐藏旧密码列
    ...(isMobileView ? [] : [{
      title: '旧密码',
      dataIndex: ['log', 'oldPassword'],
      key: 'oldPassword',
      width: 120,
      render: (password) => (
        <Space>
          <code style={{ fontSize: '12px' }}>
            {password || '-'}
          </code>
          {password && (
            <Button
              type="link"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => {
                navigator.clipboard.writeText(password);
                message.success('旧密码已复制');
              }}
              style={{ padding: '0 4px' }}
            />
          )}
        </Space>
      ),
    }]),
    {
      title: isMobileView ? '新密码' : '新密码',
      dataIndex: ['log', 'newPassword'],
      key: 'newPassword',
      width: isMobileView ? 100 : 120,
      render: (password) => (
        <Space>
          <code style={{ fontSize: isMobileView ? '10px' : '12px' }}>
            {password || '-'}
          </code>
          {password && (
            <Button
              type="link"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => {
                navigator.clipboard.writeText(password);
                message.success('新密码已复制');
              }}
              style={{ padding: '0 4px' }}
            />
          )}
        </Space>
      ),
    },
    // 移动端隐藏窗口ID列
    ...(isMobileView ? [] : [{
      title: '窗口ID',
      key: 'windowId',
      width: 100,
      render: (_, record) => {
        const windowId = record.log?.windowId;
        if (windowId) {
          return (
            <a
              href={`http://localhost:3000/join-account/windows/${windowId}`}
              target="_blank"
              rel="noopener noreferrer"
              style={{ color: '#1890ff', textDecoration: 'none' }}
            >
              {windowId}
            </a>
          );
        }
        return '-';
      },
    }]),
    // 移动端隐藏操作人列
    ...(isMobileView ? [] : [{
      title: '操作人',
      dataIndex: ['log', 'createdBy'],
      key: 'createdBy',
      width: 100,
      render: (createdBy) => (
        <Tag icon={createdBy === 'SYSTEM' ? <RobotOutlined /> : <UserOutlined />}>
          {createdBy}
        </Tag>
      ),
    }]),
    // 移动端隐藏变更时间列
    ...(isMobileView ? [] : [{
      title: '变更时间',
      dataIndex: ['log', 'createdAt'],
      key: 'createdAt',
      width: 150,
      render: (time) => moment(time).format('YYYY-MM-DD HH:mm:ss'),
    }]),
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 60 : 80,
      fixed: isMobileView ? false : 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                Modal.info({
                  title: '密码变更详情',
                  content: (
                    <div>
                      <p><strong>Zoom账号:</strong> {record.zoomUser?.email || `ID: ${record.log?.zoomUserId}`}</p>
                      <p><strong>变更类型:</strong> {getChangeTypeText(record.log?.changeType)}</p>
                      <p><strong>窗口ID:</strong> {record.log?.windowId || '无'}</p>
                      <p><strong>操作人:</strong> {record.log?.createdBy}</p>
                      <p><strong>变更时间:</strong> {moment(record.log?.createdAt).format('YYYY-MM-DD HH:mm:ss')}</p>
                    </div>
                  ),
                  width: isMobileView ? 300 : 500,
                });
              }}
              style={{ fontSize: isMobileView ? '11px' : '14px' }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: isMobileView ? '12px' : '24px' }}>
      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总变更次数"
              value={statistics.totalCount || 0}
              prefix={<KeyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="窗口开启"
              value={statistics.changeTypeStats?.WINDOW_OPEN || 0}
              valueStyle={{ color: '#52c41a' }}
              prefix={<SyncOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="窗口关闭"
              value={statistics.changeTypeStats?.WINDOW_CLOSE || 0}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<SyncOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="手动变更"
              value={statistics.changeTypeStats?.MANUAL || 0}
              valueStyle={{ color: '#1890ff' }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card
        title={
          <Space>
            <KeyOutlined />
            密码变更日志
            <Tooltip title="查询Join Account的密码变更历史记录">
              <InfoCircleOutlined style={{ color: '#1890ff' }} />
            </Tooltip>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                fetchLogs();
                fetchStatistics();
              }}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<KeyOutlined />}
              onClick={() => setChangePasswordModalVisible(true)}
            >
              手动变更密码
            </Button>
          </Space>
        }
      >
        {/* 筛选条件 */}
        <div style={{ marginBottom: 16, padding: 16, background: '#fafafa', borderRadius: 6 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Input
                placeholder="Zoom账号ID"
                prefix={<SearchOutlined />}
                value={filters.zoomUserId}
                onChange={(e) => setFilters(prev => ({ ...prev, zoomUserId: e.target.value }))}
                allowClear
              />
            </Col>
            <Col span={6}>
              <Select
                placeholder="变更类型"
                value={filters.changeType}
                onChange={(value) => setFilters(prev => ({ ...prev, changeType: value }))}
                allowClear
                style={{ width: '100%' }}
              >
                <Option value="WINDOW_OPEN">窗口开启</Option>
                <Option value="WINDOW_CLOSE">窗口关闭</Option>
                <Option value="MANUAL">手动变更</Option>
              </Select>
            </Col>
            <Col span={6}>
              <Input
                placeholder="操作人"
                value={filters.createdBy}
                onChange={(e) => setFilters(prev => ({ ...prev, createdBy: e.target.value }))}
                allowClear
              />
            </Col>
            <Col span={6}>
              <RangePicker
                placeholder={['开始时间', '结束时间']}
                value={filters.timeRange}
                onChange={(dates) => {
                  if (dates) {
                    setFilters(prev => ({
                      ...prev,
                      startTime: dates[0].toISOString(),
                      endTime: dates[1].toISOString(),
                      timeRange: dates
                    }));
                  } else {
                    setFilters(prev => {
                      const { startTime, endTime, timeRange, ...rest } = prev;
                      return rest;
                    });
                  }
                }}
                style={{ width: '100%' }}
              />
            </Col>
          </Row>
        </div>

        <div data-page="join-account-password-logs">
          <Table
            columns={columns}
            dataSource={logs}
            rowKey={(record) => record.log?.id}
            loading={loading}
            size={isMobileView ? 'small' : 'middle'}
            scroll={{ x: isMobileView ? 400 : 1000 }}
            pagination={{
              ...pagination,
              showSizeChanger: !isMobileView,
              showQuickJumper: !isMobileView,
              showTotal: (total, range) =>
                isMobileView
                  ? `${range[0]}-${range[1]} / ${total}`
                  : `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              simple: isMobileView,
              size: isMobileView ? 'small' : 'default',
              onChange: (page, pageSize) => {
                setPagination(prev => ({ ...prev, current: page, pageSize }));
              },
            }}
          />
        </div>
      </Card>

      {/* 手动变更密码模态框 */}
      <Modal
        title="手动变更密码"
        open={changePasswordModalVisible}
        onCancel={() => {
          setChangePasswordModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={400}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleChangePassword}
        >
          <Form.Item
            name="zoomUserId"
            label="Zoom账号ID"
            rules={[
              { required: true, message: '请输入Zoom账号ID' },
              { type: 'number', message: '请输入有效的账号ID' }
            ]}
          >
            <InputNumber
              placeholder="请输入Zoom账号ID"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setChangePasswordModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                变更密码
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default JoinAccountPasswordLogs;
