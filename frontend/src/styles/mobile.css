/* 移动端适配样式 */

/* 禁用移动端下拉刷新 */
html, body {
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

/* 禁用iOS Safari的下拉刷新 */
body {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  overscroll-behavior-y: none;
}

/* 允许文本选择但禁用下拉刷新 */
input, textarea, [contenteditable] {
  -webkit-user-select: text;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* 基础响应式断点 */
@media (max-width: 768px) {
  /* 全局移动端样式 */
  .mobile-container {
    padding: 12px !important;
    margin: 0 !important;
  }

  /* 卡片样式优化 */
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .ant-card-small > .ant-card-head {
    padding: 0 12px;
    min-height: 40px;
  }

  .ant-card-small > .ant-card-body {
    padding: 12px;
  }

  /* 统计卡片移动端优化 */
  .mobile-statistic-card {
    text-align: center;
    margin-bottom: 8px;
  }

  .mobile-statistic-card .ant-statistic-title {
    font-size: 12px;
    margin-bottom: 4px;
  }

  .mobile-statistic-card .ant-statistic-content {
    font-size: 18px;
  }

  /* 任务卡片样式 */
  .mobile-task-card {
    border-radius: 8px;
    margin-bottom: 8px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
  }

  .mobile-task-card:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  }

  .mobile-task-card .task-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .mobile-task-card .task-info {
    flex: 1;
  }

  .mobile-task-card .task-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .mobile-task-card .task-meta {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
  }

  .mobile-task-card .task-meta:last-child {
    margin-bottom: 0;
  }

  /* 标签样式优化 */
  .ant-tag {
    border-radius: 4px;
    font-size: 11px;
    padding: 2px 6px;
    line-height: 1.2;
  }

  /* 按钮样式优化 */
  .ant-btn-sm {
    height: 28px;
    padding: 0 8px;
    font-size: 12px;
    border-radius: 4px;
  }

  /* 表格移动端隐藏 - 但PMI管理页面除外 */
  .ant-table-wrapper {
    display: none;
  }

  /* 各个页面的表格在移动端显示 */
  [data-page="pmi-management"] .ant-table-wrapper,
  [data-page="users"] .ant-table-wrapper,
  [data-page="pmi-schedule-management"] .ant-table-wrapper,
  [data-page="meeting-list"] .ant-table-wrapper,
  [data-page="admin-users"] .ant-table-wrapper,
  [data-page="join-account-windows"] .ant-table-wrapper,
  [data-page="dashboard-meetings"] .ant-table-wrapper,
  [data-page="zoom-meeting-dashboard"] .ant-table-wrapper,
  [data-page="log-search"] .ant-table-wrapper,
  [data-page="pending-migrations"] .ant-table-wrapper,
  [data-page="all-migrations"] .ant-table-wrapper,
  [data-page="pmi-billing-management"] .ant-table-wrapper,
  [data-page="zoom-auth"] .ant-table-wrapper,
  [data-page="zoom-users"] .ant-table-wrapper,
  [data-page="webhook-events"] .ant-table-wrapper,
  [data-page="zoom-api-logs"] .ant-table-wrapper,
  [data-page="join-account-tokens"] .ant-table-wrapper,
  [data-page="join-account-password-logs"] .ant-table-wrapper,
  [data-page="join-account-system-config"] .ant-table-wrapper,
  [data-page="version-management"] .ant-table-wrapper,
  [data-page="log-search"] .ant-table-wrapper,
  [data-page="pending-migrations"] .ant-table-wrapper,
  [data-page="all-migrations"] .ant-table-wrapper,
  [data-page="scheduled-task-management"] .ant-table-wrapper,
  [data-page="zoom-meeting-history"] .ant-table-wrapper,
  [data-page="scheduled-task-list"] .ant-table-wrapper,
  [data-page="running-tasks"] .ant-table-wrapper,
  [data-page="task-analytics-overview"] .ant-table-wrapper {
    display: block !important;
    overflow-x: auto;
  }

  /* 移动端显示列表 */
  .mobile-task-list {
    display: block;
  }

  /* 抽屉样式优化 */
  .ant-drawer-body {
    padding: 16px;
  }

  .ant-drawer-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-drawer-title {
    font-size: 16px;
    font-weight: 600;
  }

  /* 模态框移动端优化 */
  .ant-modal {
    margin: 0;
    padding-bottom: 0;
  }

  .ant-modal-content {
    border-radius: 8px 8px 0 0;
  }

  .ant-modal-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-modal-body {
    padding: 16px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .ant-modal-footer {
    padding: 12px 16px;
    border-top: 1px solid #f0f0f0;
  }

  /* 表单样式优化 */
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-form-item-label {
    padding-bottom: 4px;
  }

  .ant-form-item-label > label {
    font-size: 14px;
    font-weight: 500;
  }

  /* 输入框样式 */
  .ant-input-lg,
  .ant-picker-large {
    height: 44px;
    font-size: 16px;
    border-radius: 6px;
  }

  .ant-input,
  .ant-picker {
    border-radius: 6px;
  }

  /* 选项卡样式 */
  .ant-tabs-tab {
    padding: 8px 12px;
    font-size: 14px;
  }

  .ant-tabs-content-holder {
    padding-top: 8px;
  }

  /* 浮动按钮样式 */
  .ant-float-btn-group {
    right: 16px !important;
    bottom: 16px !important;
  }

  .ant-float-btn {
    width: 48px;
    height: 48px;
  }

  /* 分割线样式 */
  .ant-divider {
    margin: 12px 0;
  }

  /* 空状态样式 */
  .mobile-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
  }

  .mobile-empty-state .ant-empty-image {
    height: 60px;
  }

  .mobile-empty-state .ant-empty-description {
    font-size: 14px;
    color: #999;
  }

  /* 加载状态优化 */
  .ant-spin-container {
    min-height: 200px;
  }

  /* 通知样式优化 */
  .ant-notification {
    width: calc(100vw - 32px);
    max-width: 400px;
    margin-right: 16px;
  }

  .ant-message {
    top: 16px;
  }

  /* 工具提示样式 */
  .ant-tooltip {
    font-size: 12px;
  }

  /* 下拉菜单样式 */
  .ant-dropdown {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .ant-dropdown-menu {
    border-radius: 8px;
    padding: 4px;
  }

  .ant-dropdown-menu-item {
    border-radius: 4px;
    margin: 2px 0;
    padding: 8px 12px;
  }

  /* 徽章样式 */
  .ant-badge {
    font-size: 11px;
  }

  /* 头像样式 */
  .ant-avatar {
    border-radius: 6px;
  }

  /* 进度条样式 */
  .ant-progress {
    margin-bottom: 8px;
  }

  /* 时间轴样式 */
  .ant-timeline {
    padding-left: 16px;
  }

  .ant-timeline-item {
    padding-bottom: 12px;
  }

  /* 步骤条样式 */
  .ant-steps {
    font-size: 12px;
  }

  .ant-steps-item-title {
    font-size: 12px;
  }

  .ant-steps-item-description {
    font-size: 11px;
  }
}

/* 超小屏幕适配 (iPhone SE等) */
@media (max-width: 375px) {
  .mobile-container {
    padding: 8px !important;
  }

  .ant-card-small > .ant-card-body {
    padding: 8px;
  }

  .mobile-statistic-card .ant-statistic-content {
    font-size: 16px;
  }

  .ant-btn-sm {
    height: 24px;
    padding: 0 6px;
    font-size: 11px;
  }

  .ant-tag {
    font-size: 10px;
    padding: 1px 4px;
  }
}

/* 横屏适配 */
@media (max-width: 768px) and (orientation: landscape) {
  .mobile-container {
    padding: 8px 16px !important;
  }

  .ant-drawer-content-wrapper {
    height: 80% !important;
  }

  .ant-modal-body {
    max-height: 50vh;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .mobile-task-card {
    background-color: #1f1f1f;
    border-color: #303030;
  }

  .mobile-task-card:hover {
    border-color: #1890ff;
    background-color: #262626;
  }

  .mobile-empty-state {
    color: #8c8c8c;
  }
}

/* 移动端专用动画 */
@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 移动端任务卡片动画 */
.mobile-task-card {
  animation: slideInUp 0.3s ease-out;
}

.mobile-task-card:hover {
  animation: pulse 0.3s ease-in-out;
}



/* 加载动画 */
.mobile-loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 成功动画 */
.mobile-success {
  animation: bounce 0.6s ease-out;
}

/* 错误动画 */
.mobile-error {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* 浮动按钮动画 */
.ant-float-btn {
  animation: fadeIn 0.3s ease-out;
}

.ant-float-btn:hover {
  animation: pulse 0.3s ease-in-out;
}

/* 抽屉动画优化 */
.ant-drawer-content-wrapper {
  animation: slideInUp 0.3s ease-out;
}

/* 模态框动画优化 */
.ant-modal {
  animation: slideInDown 0.3s ease-out;
}

/* 通知动画优化 */
.ant-notification {
  animation: slideInDown 0.3s ease-out;
}

.ant-message {
  animation: slideInDown 0.3s ease-out;
}
