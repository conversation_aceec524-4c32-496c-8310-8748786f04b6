# 🔧 PMI任务监控页面功能增强

## 📋 需求概述

在 `https://m.zoombus.com/pmi-task-monitor` 页面进行以下增强：
1. 在"任务ID"前新增"窗口ID"列，窗口ID列值占用2行，对应这个窗口的两条task记录
2. 在"计划时间"后面新增"执行时间"列，展示实际执行时间
3. 在"执行时间"值后面用括号显示偏移时间（+代表延迟，-代表提前），仅对已执行任务显示
4. 操作栏新增"立即执行"功能，触发即刻执行任务

## ✅ 完成的修改

### 1. 桌面端表格增强

**文件**: `frontend/src/pages/PmiTaskMonitor.jsx`

**新增"窗口ID"列（跨行显示）**:
```javascript
// 数据处理函数，为窗口ID列添加rowSpan
const processTasksForRowSpan = (taskList) => {
  if (!taskList || taskList.length === 0) return [];

  // 按窗口ID分组
  const windowGroups = {};
  taskList.forEach((task, index) => {
    const windowId = task.pmiWindowId;
    if (!windowGroups[windowId]) {
      windowGroups[windowId] = [];
    }
    windowGroups[windowId].push({ ...task, originalIndex: index });
  });

  // 为每个窗口的第一个任务设置rowSpan，其他任务设置为0
  const processedTasks = [];
  Object.values(windowGroups).forEach(group => {
    group.forEach((task, groupIndex) => {
      const processedTask = { ...task };
      if (groupIndex === 0) {
        processedTask.windowRowSpan = group.length;
      } else {
        processedTask.windowRowSpan = 0;
      }
      processedTasks.push(processedTask);
    });
  });

  // 按原始顺序排序
  return processedTasks.sort((a, b) => a.originalIndex - b.originalIndex);
};

// 窗口ID列定义
{
  title: '窗口ID',
  dataIndex: 'pmiWindowId',
  key: 'pmiWindowId',
  width: 80,
  render: (windowId, record) => ({
    children: <span style={{ fontFamily: 'monospace' }}>{windowId}</span>,
    props: {
      rowSpan: record.windowRowSpan || 1,
    },
  })
}
```

**新增"执行时间"列（包含偏移时间）**:
```javascript
{
  title: '执行时间',
  dataIndex: 'actualExecutionTime',
  key: 'actualExecutionTime',
  width: 200,
  render: (time, record) => {
    if (!time) {
      if (record.status === 'SCHEDULED') {
        return <span style={{ color: '#999' }}>等待执行</span>;
      } else if (record.status === 'EXECUTING') {
        return <span style={{ color: '#1890ff' }}>执行中...</span>;
      } else {
        return <span style={{ color: '#999' }}>未执行</span>;
      }
    }

    // 计算偏移时间
    const actualTime = moment(time);
    const scheduledTime = moment(record.scheduledTime);
    const diffMinutes = actualTime.diff(scheduledTime, 'minutes');

    let offsetText = '';
    let offsetColor = '#666';

    if (diffMinutes > 0) {
      // 延迟执行
      const hours = Math.floor(diffMinutes / 60);
      const minutes = diffMinutes % 60;
      const timeText = hours > 0 ? `${hours}h${minutes}m` : `${minutes}分钟`;
      offsetText = `(+${timeText})`;
      offsetColor = '#ff4d4f';
    } else if (diffMinutes < 0) {
      // 提前执行
      const absDiffMinutes = Math.abs(diffMinutes);
      const hours = Math.floor(absDiffMinutes / 60);
      const minutes = absDiffMinutes % 60;
      const timeText = hours > 0 ? `${hours}h${minutes}m` : `${minutes}分钟`;
      offsetText = `(-${timeText})`;
      offsetColor = '#52c41a';
    } else {
      // 准时执行
      offsetText = '(准时)';
      offsetColor = '#1890ff';
    }

    return (
      <div>
        <span>{moment(time).format('MM-DD HH:mm')}</span>
        <span style={{ color: offsetColor, marginLeft: '4px', fontSize: '12px' }}>
          {offsetText}
        </span>
      </div>
    );
  }
}
```

**操作菜单中的"立即执行"**:
- 已存在完整的`handleExecuteTask`函数
- 包含确认对话框和错误处理
- 支持权限检查（需要SUPER_ADMIN权限）

### 2. 移动端卡片增强

**文件**: `frontend/src/components/mobile/MobileTaskCard.jsx`

**分离显示窗口ID、任务ID、计划时间和执行时间（含偏移）**:
```javascript
{/* 窗口ID */}
<div style={{ marginBottom: 4 }}>
  <Text type="secondary" style={{ fontSize: 12 }}>窗口: </Text>
  <Text style={{ fontSize: 12, fontFamily: 'monospace' }}>{task.pmiWindowId}</Text>
</div>

{/* 任务ID */}
<div style={{ marginBottom: 4 }}>
  <Text type="secondary" style={{ fontSize: 12 }}>任务: </Text>
  <Text style={{ fontSize: 12, fontFamily: 'monospace' }}>{task.id}</Text>
</div>

{/* 计划时间 */}
<div style={{ marginBottom: 4 }}>
  <Text type="secondary" style={{ fontSize: 12 }}>计划: </Text>
  <Text style={{ fontSize: 12, color: '#666' }}>
    {formatMobileTime(task.scheduledTime)}
  </Text>
</div>

{/* 执行时间（含偏移） */}
<div style={{ marginBottom: 4 }}>
  <Text type="secondary" style={{ fontSize: 12 }}>执行: </Text>
  {task.actualExecutionTime ? (
    <span>
      <Text style={{ fontSize: 12, color: timeInfo.color }}>
        {formatMobileTime(task.actualExecutionTime)}
      </Text>
      {(() => {
        // 计算偏移时间
        const actualTime = moment(task.actualExecutionTime);
        const scheduledTime = moment(task.scheduledTime);
        const diffMinutes = actualTime.diff(scheduledTime, 'minutes');

        let offsetText = '';
        let offsetColor = '#666';

        if (diffMinutes > 0) {
          // 延迟执行
          const hours = Math.floor(diffMinutes / 60);
          const minutes = diffMinutes % 60;
          const timeText = hours > 0 ? `${hours}h${minutes}m` : `${minutes}分钟`;
          offsetText = `(+${timeText})`;
          offsetColor = '#ff4d4f';
        } else if (diffMinutes < 0) {
          // 提前执行
          const absDiffMinutes = Math.abs(diffMinutes);
          const hours = Math.floor(absDiffMinutes / 60);
          const minutes = absDiffMinutes % 60;
          const timeText = hours > 0 ? `${hours}h${minutes}m` : `${minutes}分钟`;
          offsetText = `(-${timeText})`;
          offsetColor = '#52c41a';
        } else {
          // 准时执行
          offsetText = '(准时)';
          offsetColor = '#1890ff';
        }

        return (
          <Text style={{ fontSize: 11, color: offsetColor, marginLeft: 4 }}>
            {offsetText}
          </Text>
        );
      })()}
    </span>
  ) : (
    <Text style={{ fontSize: 12, color: '#999' }}>
      {task.status === 'SCHEDULED' ? '等待执行' :
       task.status === 'EXECUTING' ? '执行中...' : '未执行'}
    </Text>
  )}
</div>
```

### 3. 表格滚动宽度调整

由于新增了"窗口ID"列和"执行时间"列（宽度增加到200px以容纳偏移时间），将表格滚动宽度从1200px调整为1500px，确保所有列都能正常显示。

### 4. 权限配置

**前端权限检查**:
- `canExecuteTask`: 需要SUPER_ADMIN权限
- `canCancelTask`: 需要SUPER_ADMIN权限  
- `canRescheduleTask`: 需要ADMIN或SUPER_ADMIN权限

**后端API权限**:
- `POST /{id}/execute`: 需要SUPER_ADMIN权限
- `DELETE /{id}`: 需要SUPER_ADMIN权限
- `PUT /{id}/reschedule`: 需要ADMIN或SUPER_ADMIN权限

## 🎯 功能特性

### 窗口ID跨行显示

**实现原理**:
1. **数据分组**: 按`pmiWindowId`对任务进行分组
2. **rowSpan计算**: 每个窗口的第一个任务设置`rowSpan`为该窗口的任务数量
3. **其他任务**: 同一窗口的其他任务`rowSpan`设置为0（不显示）
4. **视觉效果**: 窗口ID在表格中跨行显示，清晰展示任务归属关系

**数据结构**:
- 每个PMI窗口通常包含2个任务：开启任务(PMI_WINDOW_OPEN)和关闭任务(PMI_WINDOW_CLOSE)
- 窗口ID列会跨越这两行，直观显示任务的关联关系

### 执行时间显示逻辑

1. **有执行时间**: 显示具体的执行时间（MM-DD HH:mm格式），去掉相对时间显示
2. **SCHEDULED状态**: 显示"等待执行"
3. **EXECUTING状态**: 显示"执行中..."
4. **其他状态**: 显示"未执行"

### 偏移时间显示逻辑

**仅对已执行任务显示偏移时间**，在执行时间后面用括号显示：

1. **延迟执行**: 红色显示 `(+时间)` (如: (+30分钟), (+2h15m))
2. **提前执行**: 绿色显示 `(-时间)` (如: (-15分钟), (-1h30m))
3. **准时执行**: 蓝色显示 `(准时)`

**未执行任务不显示偏移时间**，只显示状态：
- **SCHEDULED状态**: 显示"等待执行"
- **EXECUTING状态**: 显示"执行中..."
- **其他状态**: 显示"未执行"

**颜色编码**:
- 🔴 **红色**: 延迟执行 `(+时间)`
- 🟢 **绿色**: 提前执行 `(-时间)`
- 🔵 **蓝色**: 准时执行 `(准时)`

### 立即执行功能

1. **权限检查**: 只有SUPER_ADMIN用户可以看到和使用
2. **确认对话框**: 执行前需要用户确认
3. **状态限制**: 只能执行SCHEDULED状态的任务
4. **反馈机制**: 执行成功/失败都有相应提示
5. **数据刷新**: 执行后自动刷新任务列表和统计信息

### 多端支持

1. **桌面端**: 表格形式，操作通过下拉菜单
2. **移动端**: 卡片形式，操作通过菜单和详情页按钮
3. **响应式**: 自动适配不同屏幕尺寸

## 🔄 数据流程

### 执行时间数据来源

1. **后端字段**: `PmiScheduleWindowTask.actualExecutionTime`
2. **DTO映射**: `PmiScheduledTaskInfo.actualExecutionTime`
3. **前端显示**: 通过API获取并在界面展示

### 立即执行流程

1. **前端触发**: 用户点击"立即执行"按钮
2. **权限验证**: 检查用户是否有SUPER_ADMIN权限
3. **确认对话框**: 用户确认执行操作
4. **API调用**: `POST /pmi-scheduled-tasks/{id}/execute`
5. **后端处理**: `PmiTaskManagementService.executePmiTask()`
6. **状态更新**: 任务状态更新为EXECUTING，设置执行时间
7. **前端刷新**: 重新加载任务列表显示最新状态

## 📱 移动端优化

1. **触觉反馈**: 操作时提供触觉反馈
2. **大按钮**: 适合触摸操作的按钮尺寸
3. **抽屉详情**: 完整的任务详情展示
4. **下拉刷新**: 支持下拉刷新功能

## 🔍 测试要点

### 功能测试
1. 验证"执行时间"列正确显示
2. 测试"立即执行"功能是否正常工作
3. 检查权限控制是否生效
4. 验证移动端和桌面端的一致性

### 状态测试
1. SCHEDULED任务显示"等待执行"
2. EXECUTING任务显示"执行中..."
3. 已执行任务显示具体执行时间
4. 立即执行后状态正确更新

### 权限测试
1. SUPER_ADMIN用户可以看到"立即执行"按钮
2. 普通用户无法看到"立即执行"按钮
3. API调用时正确验证权限

## 📋 修改文件清单

1. **`frontend/src/pages/PmiTaskMonitor.jsx`** - 桌面端表格增强
   - 新增窗口ID列和跨行显示逻辑
   - 数据处理函数`processTasksForRowSpan`
   - 任务详情抽屉字段顺序调整（窗口ID在任务ID前）
   - 表格滚动宽度调整为1500px

2. **`frontend/src/components/mobile/MobileTaskCard.jsx`** - 移动端卡片增强
   - 新增窗口ID和任务ID显示
   - 保持与桌面端一致的字段顺序

3. **`pmi_task_monitor_enhancement.md`** - 文档更新
   - 功能说明和实现细节

这些增强功能提供了更完整的任务监控体验，用户可以清楚地看到：
- **窗口关联关系**: 通过跨行显示的窗口ID，直观了解任务归属
- **任务标识**: 窗口ID和任务ID的清晰展示
- **执行时间**: 计划时间和实际执行时间（含偏移）
- **操作功能**: 立即执行等管理功能

特别是窗口ID的跨行显示，让用户能够一目了然地看到同一个PMI窗口下的开启和关闭任务，大大提升了数据的可读性和管理效率。
