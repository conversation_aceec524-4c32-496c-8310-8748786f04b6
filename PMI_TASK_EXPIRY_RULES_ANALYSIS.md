# PMI精准管理任务过期规则分析报告

## 概述

本报告详细分析了当前PMI精准管理系统中对于open类型任务的过期规则和处理机制。

## 当前过期规则

### 1. Open类型任务过期规则

#### 1.1 基本原则
**Open任务的过期判断基于窗口关闭时间，而非固定的延迟时间限制。**

```java
// 核心逻辑：只要当前时间还在窗口关闭时间之前，开启任务就仍然有效
private boolean isOpenTaskStillValid(PmiScheduleWindowTask task, LocalDateTime now) {
    PmiScheduleWindow window = windowRepository.findById(task.getPmiWindowId());
    LocalDateTime windowCloseTime = window.getEndDateTime();
    
    // 关键判断：当前时间 < 窗口关闭时间
    boolean isValid = now.isBefore(windowCloseTime);
    return isValid;
}
```

#### 1.2 具体规则
1. **有效期判断**: 开启任务只要在对应PMI窗口的关闭时间之前，都被认为是有效的
2. **无时间限制**: 不像关闭任务有5分钟的固定延迟限制，开启任务没有固定的过期时间
3. **窗口导向**: 过期与否完全取决于PMI窗口的生命周期

### 2. 过期任务处理流程

#### 2.1 系统启动时的处理
```java
@PostConstruct
public void initializePmiScheduledTasks() {
    List<PmiScheduleWindowTask> pendingTasks = taskRepository.findByStatus(SCHEDULED);
    
    for (PmiScheduleWindowTask task : pendingTasks) {
        if (task.getScheduledTime().isBefore(now)) {
            // 时间已过期，但仍需检查是否在有效期内
            handleExpiredPmiTask(task);
        } else {
            // 重新调度未来的任务
            reschedulePmiTask(task);
        }
    }
}
```

#### 2.2 过期任务处理逻辑
```java
private void handleExpiredPmiTask(PmiScheduleWindowTask task) {
    LocalDateTime now = LocalDateTime.now();
    long delayMinutes = Duration.between(task.getScheduledTime(), now).toMinutes();

    if (task.getTaskType() == PMI_WINDOW_OPEN) {
        if (isOpenTaskStillValid(task, now)) {
            // 仍在有效期内，立即执行
            log.info("开启任务仍在有效期内，立即执行: taskId={}, delay={}分钟", 
                    task.getId(), delayMinutes);
            taskExecutor.executeOpenTask(task.getPmiWindowId(), task.getId());
        } else {
            // 已超过窗口关闭时间，标记为失败
            log.warn("开启任务已超过窗口关闭时间，标记为失败: taskId={}, delay={}分钟", 
                    task.getId(), delayMinutes);
            task.markAsFailed("任务过期，窗口已关闭，延迟" + delayMinutes + "分钟");
            taskRepository.save(task);
        }
    }
}
```

### 3. 配置参数

#### 3.1 立即执行配置
```yaml
pmi:
  task:
    scheduling:
      immediate-execution:
        enabled: true                    # 启用立即执行机制
        tolerance-minutes: 2             # 时间容差（分钟）
        max-delay-minutes: 60           # 最大延迟执行时间（分钟）
        enable-past-execution: true      # 允许执行过往时间的任务
```



#### 3.3 任务清理配置
```yaml
pmi:
  task:
    scheduling:
      cleanup:
        enabled: true
        retention-days: 30              # 保留已完成任务的天数
        cron-expression: "0 0 2 * * ?"  # 每天凌晨2点执行清理
```

## PMI Task 82 案例分析

### 案例详情
- **任务ID**: 82
- **任务类型**: PMI_WINDOW_OPEN (PMI开启)
- **PMI号码**: 9271906174
- **计划执行时间**: 2025-08-27T00:00:00 (今天凌晨00:00)
- **任务创建时间**: 2025-08-27T22:16:10 (今天晚上22:16)
- **窗口关闭时间**: 2025-08-27T23:59:00 (今天晚上23:59)
- **当前状态**: 已调度

### 问题分析

#### 1. 时间逻辑问题
- 任务计划在凌晨00:00执行，但在晚上22:16才创建
- 创建时间晚于计划执行时间约22小时16分钟

#### 2. 系统行为
根据当前的过期规则：
1. **系统启动检查**: 如果系统在22:16之后重启，会检测到task 82的计划时间已过期
2. **有效性检查**: 调用`isOpenTaskStillValid()`检查窗口是否仍然有效
3. **执行决策**: 由于当前时间(22:27)仍在窗口关闭时间(23:59)之前，任务应该被立即执行

#### 3. 可能的原因
Task 82没有执行的可能原因：
1. **系统未重启**: 如果系统在task创建后没有重启，过期检查可能没有触发
2. **精准调度未启用**: 如果`enable-precise-scheduling: false`，可能使用旧的轮询机制
3. **任务执行器异常**: 任务被标记为立即执行，但执行器可能遇到异常
4. **数据库状态不一致**: 任务状态可能没有正确更新

## 建议和改进

### 1. 立即检查和修复
```sql
-- 检查task 82的当前状态
SELECT * FROM t_pmi_schedule_window_tasks WHERE id = 82;

-- 检查对应的PMI窗口状态
SELECT * FROM t_pmi_schedule_windows WHERE id = 2053;

-- 检查是否有执行日志
SELECT * FROM logs WHERE content LIKE '%task.*82%' ORDER BY created_at DESC LIMIT 10;
```

### 2. 手动触发执行
如果确认任务仍在有效期内，可以手动触发：
```java
// 通过管理接口手动重新调度任务
dynamicTaskManager.reschedulePmiTask(taskId);

// 或者直接执行任务
taskExecutor.executeOpenTask(windowId, taskId);
```

### 3. 监控增强
建议增加以下监控：
1. **过期任务告警**: 当任务延迟超过一定时间时发送告警
2. **执行状态跟踪**: 详细记录任务从调度到执行的每个步骤
3. **窗口有效性监控**: 监控窗口状态变化对任务执行的影响

### 4. 配置优化
```yaml
pmi:
  task:
    scheduling:
      # 确保启用精准调度
      enable-precise-scheduling: true
      
      # 增强监控
      enable-task-monitoring: true
      
      # 优化立即执行配置
      immediate-execution:
        enabled: true
        tolerance-minutes: 5          # 增加容差时间
        max-delay-minutes: 120        # 增加最大延迟时间
        enable-past-execution: true
```

## 总结

当前PMI精准管理系统对于open类型任务的过期规则是**基于窗口生命周期的智能判断**，而非简单的时间限制。这种设计确保了只要PMI窗口仍然有效，即使任务创建时间晚于计划执行时间，也能够正确执行。

对于PMI Task 82的情况，根据当前规则，该任务应该在系统检测到过期时立即执行，因为窗口仍在有效期内。如果任务没有执行，需要进一步检查系统状态、配置和执行日志来确定具体原因。
